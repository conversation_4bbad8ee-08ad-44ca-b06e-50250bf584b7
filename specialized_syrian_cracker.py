#!/usr/bin/env python3
"""
SPECIALIZED SYRIAN UNIVERSITY PASSWORD CRACKER
Target: Focus on Syrian/Arabic patterns and university-specific passwords
Status: ACTIVE SPECIALIZED CRACKING
"""

import hashlib
import itertools
import time

# The confirmed MD5 hash
target_hash = "00003000000080030040010001000000"
target_student = "4230105"

def md5_hash(text):
    """Generate MD5 hash of text"""
    return hashlib.md5(text.encode('utf-8', errors='ignore')).hexdigest()

def syrian_arabic_patterns():
    """Try Syrian and Arabic specific patterns"""
    print(f"\n🇸🇾 SYRIAN AND ARABIC PATTERNS")
    
    # Arabic/Syrian specific words (transliterated)
    arabic_words = [
        # Common Arabic names
        "roaa", "roa", "ruaa", "roua", "rowa",
        "ghneem", "ghnem", "ghanem", "ghaneem", "ghaniem",
        
        # Syrian cities and places
        "syria", "suriya", "sham", "dimashq", "damascus", "haleb", "aleppo",
        "homs", "hama", "lattakia", "tartous", "deir", "raqqa", "qamishli",
        
        # University related Arabic
        "jamea", "jameaa", "university", "kulliya", "college", "talib", "student",
        "handasa", "engineering", "computer", "hasub", "barnamaj", "software",
        
        # Common Arabic words
        "ahlan", "marhaba", "habibi", "habibti", "yalla", "khalas", "shukran",
        "allah", "bismillah", "inshallah", "mashallah", "alhamdulillah",
        "salam", "peace", "salaam", "ahla", "wsahla",
        
        # Syrian dialect
        "tfaddal", "tfaddali", "keefak", "keefik", "shu", "akhbar", "tamam",
        "zain", "helou", "hilwa", "kteer", "shway", "bass", "khalas"
    ]
    
    # Years relevant to Syrian students
    years = ["2020", "2019", "2018", "2021", "2022", "2023", "2024", "1999", "2000", "2001"]
    
    # Common symbols in Arabic keyboards
    symbols = ["!", "@", "#", "$", "%", "^", "&", "*", "(", ")", "_", "+", "-", "="]
    
    total_tested = 0
    
    for word in arabic_words:
        # Test word variations
        variations = [
            word.lower(),
            word.upper(),
            word.capitalize(),
            word.title()
        ]
        
        for variant in variations:
            # Test with years
            for year in years:
                year_combinations = [
                    f"{variant}{year}",
                    f"{year}{variant}",
                    f"{variant}_{year}",
                    f"{variant}-{year}",
                    f"{variant}.{year}"
                ]
                
                for combo in year_combinations:
                    # Test with symbols
                    for symbol in symbols[:8]:  # Limit symbols to avoid too many combinations
                        symbol_combinations = [
                            f"{combo}{symbol}",
                            f"{symbol}{combo}",
                            f"{combo}{symbol}{symbol}",
                            f"{symbol}{combo}{symbol}"
                        ]
                        
                        for password in symbol_combinations:
                            if len(password) > 25:  # Skip very long passwords
                                continue
                                
                            hash_result = md5_hash(password)
                            total_tested += 1
                            
                            if total_tested % 1000 == 0:
                                print(f"   Tested {total_tested} Arabic patterns...")
                            
                            if hash_result == target_hash:
                                print(f"\n🎉 PASSWORD FOUND!")
                                print(f"🔑 Password: {password}")
                                return password
    
    print(f"Tested {total_tested} Arabic/Syrian patterns")
    return None

def engineering_student_patterns():
    """Try engineering and computer science specific patterns"""
    print(f"\n💻 ENGINEERING STUDENT PATTERNS")
    
    # Engineering/CS terms
    eng_terms = [
        # Programming languages
        "python", "java", "cpp", "javascript", "php", "html", "css", "sql",
        "react", "angular", "vue", "node", "django", "flask", "spring",
        
        # Engineering concepts
        "algorithm", "data", "structure", "database", "network", "system",
        "software", "hardware", "code", "program", "debug", "compile",
        "function", "class", "object", "array", "loop", "variable",
        
        # University courses (common in Syria)
        "math", "calculus", "physics", "chemistry", "statistics", "discrete",
        "linear", "algebra", "geometry", "probability", "logic", "circuit",
        
        # Grades and academic
        "grade", "gpa", "exam", "test", "project", "thesis", "research",
        "study", "learn", "book", "library", "lab", "practical"
    ]
    
    # Student ID patterns
    id_patterns = [
        target_student,  # 4230105
        target_student[:6],  # 423010
        target_student[:5],  # 42301
        target_student[:4],  # 4230
        target_student[1:],  # 230105
        target_student[2:],  # 30105
        target_student[-4:],  # 0105
        target_student[-3:],  # 105
        "423",  # Department code
        "2010",  # Possible year
        "2020"   # Graduation year
    ]
    
    total_tested = 0
    
    for term in eng_terms:
        for id_part in id_patterns:
            # Combine engineering terms with ID parts
            combinations = [
                f"{term}{id_part}",
                f"{id_part}{term}",
                f"{term}_{id_part}",
                f"{term}-{id_part}",
                f"{term}.{id_part}",
                f"{term}@{id_part}",
                f"{term}#{id_part}",
                f"{term}!{id_part}",
                f"{id_part}!{term}",
                f"{id_part}@{term}"
            ]
            
            for combo in combinations:
                # Test with case variations
                case_variations = [
                    combo.lower(),
                    combo.upper(),
                    combo.capitalize()
                ]
                
                for password in case_variations:
                    if len(password) > 25:
                        continue
                        
                    hash_result = md5_hash(password)
                    total_tested += 1
                    
                    if total_tested % 1000 == 0:
                        print(f"   Tested {total_tested} engineering patterns...")
                    
                    if hash_result == target_hash:
                        print(f"\n🎉 PASSWORD FOUND!")
                        print(f"🔑 Password: {password}")
                        return password
    
    print(f"Tested {total_tested} engineering patterns")
    return None

def personal_info_patterns():
    """Try patterns based on personal information"""
    print(f"\n👤 PERSONAL INFORMATION PATTERNS")
    
    # Personal info we know
    personal_info = {
        "name": ["roaa", "roa", "ruaa"],
        "surname": ["ghneem", "ghnem", "ghanem"],
        "id": [target_student],
        "email_prefix": [target_student],
        "department": ["engineering", "handasa", "eng"],
        "major": ["software", "computer", "cs", "se"],
        "year": ["2020", "20", "2019", "2021"],
        "university": ["spu", "syria", "private"]
    }
    
    # Common password patterns
    patterns = [
        "{name}{year}",
        "{name}{id}",
        "{name}_{year}",
        "{name}@{year}",
        "{name}!{year}",
        "{name}{year}!",
        "{surname}{year}",
        "{surname}{id}",
        "{name}{surname}",
        "{name}.{surname}",
        "{name}_{surname}",
        "{name}{surname}{year}",
        "{university}{name}",
        "{university}{id}",
        "{department}{id}",
        "{major}{id}",
        "{name}{major}",
        "{name}{department}",
        "I{name}{year}",  # "I" prefix common in passwords
        "My{name}{year}",
        "{name}Is{year}",
        "{name}@{university}",
        "{id}@{university}",
        "{name}{id}@",
        "{name}{year}@spu",
        "{university}{name}{year}"
    ]
    
    total_tested = 0
    
    for pattern in patterns:
        # Get all combinations of personal info
        for name in personal_info["name"]:
            for surname in personal_info["surname"]:
                for year in personal_info["year"]:
                    for university in personal_info["university"]:
                        for department in personal_info["department"]:
                            for major in personal_info["major"]:
                                for id_val in personal_info["id"]:
                                    try:
                                        # Format the pattern
                                        password = pattern.format(
                                            name=name,
                                            surname=surname,
                                            year=year,
                                            university=university,
                                            department=department,
                                            major=major,
                                            id=id_val
                                        )
                                        
                                        # Test case variations
                                        case_variations = [
                                            password.lower(),
                                            password.upper(),
                                            password.capitalize(),
                                            password.title()
                                        ]
                                        
                                        for variant in case_variations:
                                            if len(variant) > 25:
                                                continue
                                                
                                            hash_result = md5_hash(variant)
                                            total_tested += 1
                                            
                                            if total_tested % 1000 == 0:
                                                print(f"   Tested {total_tested} personal patterns...")
                                            
                                            if hash_result == target_hash:
                                                print(f"\n🎉 PASSWORD FOUND!")
                                                print(f"🔑 Password: {variant}")
                                                return variant
                                                
                                    except KeyError:
                                        continue
    
    print(f"Tested {total_tested} personal patterns")
    return None

def keyboard_patterns():
    """Try keyboard patterns and common typing patterns"""
    print(f"\n⌨️ KEYBOARD PATTERNS")
    
    # Common keyboard patterns
    keyboard_patterns = [
        # QWERTY patterns
        "qwerty", "qwertyuiop", "asdfgh", "asdfghjkl", "zxcvbn", "zxcvbnm",
        "123456", "1234567890", "098765", "0987654321",
        
        # Arabic keyboard patterns (transliterated)
        "ضصثق", "شسيب", "تنمك",  # Arabic QWERTY
        
        # Number pad patterns
        "147258", "159357", "123789", "987321",
        
        # Common sequences
        "abcdef", "fedcba", "abcd1234", "1234abcd",
        
        # Repeated patterns
        "111111", "222222", "123123", "321321", "abc123", "123abc"
    ]
    
    # Combine with student info
    student_additions = ["roaa", "4230105", "2020", "spu", "syria"]
    
    total_tested = 0
    
    for pattern in keyboard_patterns:
        # Test pattern alone
        hash_result = md5_hash(pattern)
        total_tested += 1
        
        if hash_result == target_hash:
            print(f"\n🎉 PASSWORD FOUND!")
            print(f"🔑 Password: {pattern}")
            return pattern
        
        # Test with student additions
        for addition in student_additions:
            combinations = [
                f"{pattern}{addition}",
                f"{addition}{pattern}",
                f"{pattern}_{addition}",
                f"{pattern}@{addition}",
                f"{pattern}!{addition}",
                f"{addition}!{pattern}"
            ]
            
            for combo in combinations:
                if len(combo) > 25:
                    continue
                    
                hash_result = md5_hash(combo)
                total_tested += 1
                
                if total_tested % 500 == 0:
                    print(f"   Tested {total_tested} keyboard patterns...")
                
                if hash_result == target_hash:
                    print(f"\n🎉 PASSWORD FOUND!")
                    print(f"🔑 Password: {combo}")
                    return combo
    
    print(f"Tested {total_tested} keyboard patterns")
    return None

def main():
    print("="*80)
    print(f"🚨 SPECIALIZED SYRIAN UNIVERSITY PASSWORD CRACKER")
    print("🎯 Target: Focus on Syrian/Arabic patterns and university-specific passwords")
    print("🔥 Status: ACTIVE SPECIALIZED CRACKING")
    print("="*80)
    
    print(f"🎯 TARGET INFORMATION:")
    print(f"   Student: Roaa Ghneem (Syrian)")
    print(f"   ID: {target_student}")
    print(f"   University: SPU (Syria Private University)")
    print(f"   Major: Software Engineering")
    print(f"   Year: 2020 (graduated)")
    print(f"   Hash: {target_hash}")
    
    # Try specialized cracking methods
    password = None
    start_time = time.time()
    
    # Method 1: Syrian/Arabic patterns
    if not password:
        password = syrian_arabic_patterns()
    
    # Method 2: Engineering student patterns
    if not password:
        password = engineering_student_patterns()
    
    # Method 3: Personal information patterns
    if not password:
        password = personal_info_patterns()
    
    # Method 4: Keyboard patterns
    if not password:
        password = keyboard_patterns()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # Final result
    print(f"\n🏆 SPECIALIZED CRACKING COMPLETED")
    print(f"⏱️ Total time: {total_time:.2f} seconds")
    
    if password:
        print(f"\n🎉 SUCCESS! SPECIALIZED PASSWORD CRACKED!")
        print(f"🔑 Student {target_student} Password: {password}")
        print(f"🔐 MD5 Hash: {target_hash}")
        print(f"✅ Verification: {md5_hash(password) == target_hash}")
        
        # Save result
        with open(f'SPECIALIZED_CRACKED_PASSWORD_{target_student}.txt', 'w') as f:
            f.write(f"SPECIALIZED PASSWORD CRACKING SUCCESS\n")
            f.write(f"=" * 50 + "\n\n")
            f.write(f"Student: Roaa Ghneem\n")
            f.write(f"ID: {target_student}\n")
            f.write(f"Password: {password}\n")
            f.write(f"Hash: {target_hash}\n")
            f.write(f"Cracking method: Specialized Syrian/University patterns\n")
            f.write(f"Time: {total_time:.2f} seconds\n")
        
        print(f"💾 Result saved!")
        
    else:
        print(f"\n❌ SPECIALIZED CRACKING UNSUCCESSFUL")
        print(f"💡 The password might be:")
        print(f"   - Very long (>15 characters)")
        print(f"   - Uses uncommon character combinations")
        print(f"   - Contains special Unicode characters")
        print(f"   - Uses a completely different pattern")
        print(f"\n🔧 NEXT RECOMMENDATIONS:")
        print(f"   - Use hashcat with Syrian wordlists")
        print(f"   - Try online rainbow tables")
        print(f"   - Use GPU-accelerated cracking")
        print(f"   - Consider the password might be randomly generated")
    
    return password

if __name__ == "__main__":
    main()
