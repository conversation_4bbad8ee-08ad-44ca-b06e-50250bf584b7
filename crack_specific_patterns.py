#!/usr/bin/env python3
"""
CRACK SPECIFIC PATTERNS FOR MD5 HASH
Target: Try specific patterns that might produce the unusual hash with many zeros
Status: ACTIVE SPECIFIC PATTERN CRACKING
"""

import hashlib
import string

# The MD5 hash we extracted
target_hash = "00003000000080030040010001000000"

def md5_hash(text):
    """Generate MD5 hash of text"""
    return hashlib.md5(text.encode()).hexdigest()

def try_empty_and_special():
    """Try empty strings and special characters"""
    print(f"\n🔍 TRYING EMPTY AND SPECIAL PATTERNS")
    
    special_patterns = [
        "",  # Empty string
        " ",  # Single space
        "  ",  # Double space
        "\t",  # Tab
        "\n",  # Newline
        "\r",  # Carriage return
        "\r\n",  # Windows newline
        "0",  # Single zero
        "00",  # Double zero
        "000",  # Triple zero
        "null",  # null string
        "NULL",  # NULL string
        "nil",  # nil
        "NIL",  # NIL
        "none",  # none
        "NONE",  # NONE
        "undefined",  # undefined
        "UNDEFINED",  # UNDEFINED
    ]
    
    for pattern in special_patterns:
        hash_result = md5_hash(pattern)
        print(f"Testing special: '{pattern}' -> {hash_result}")
        
        if hash_result == target_hash:
            print(f"\n🎉 PASSWORD FOUND!")
            print(f"🔑 Plaintext Password: '{pattern}'")
            return pattern
    
    return None

def try_single_characters():
    """Try single characters"""
    print(f"\n🔍 TRYING SINGLE CHARACTERS")
    
    # All printable ASCII characters
    for char in string.printable:
        hash_result = md5_hash(char)
        print(f"Testing char: '{char}' -> {hash_result}")
        
        if hash_result == target_hash:
            print(f"\n🎉 PASSWORD FOUND!")
            print(f"🔑 Plaintext Password: '{char}'")
            return char
    
    return None

def try_unicode_characters():
    """Try common Unicode characters"""
    print(f"\n🔍 TRYING UNICODE CHARACTERS")
    
    # Common Unicode characters
    unicode_chars = [
        "أ", "ب", "ت", "ث", "ج", "ح", "خ", "د", "ذ", "ر",  # Arabic
        "α", "β", "γ", "δ", "ε", "ζ", "η", "θ", "ι", "κ",  # Greek
        "€", "£", "¥", "©", "®", "™", "°", "±", "×", "÷",  # Symbols
    ]
    
    for char in unicode_chars:
        try:
            hash_result = md5_hash(char)
            print(f"Testing Unicode: '{char}' -> {hash_result}")
            
            if hash_result == target_hash:
                print(f"\n🎉 PASSWORD FOUND!")
                print(f"🔑 Plaintext Password: '{char}'")
                return char
        except:
            continue
    
    return None

def try_binary_patterns():
    """Try binary and hex patterns"""
    print(f"\n🔍 TRYING BINARY AND HEX PATTERNS")
    
    # Binary patterns
    binary_patterns = [
        "0", "1", "00", "01", "10", "11",
        "000", "001", "010", "011", "100", "101", "110", "111",
        "0000", "0001", "0010", "0100", "1000", "1111"
    ]
    
    for pattern in binary_patterns:
        hash_result = md5_hash(pattern)
        print(f"Testing binary: '{pattern}' -> {hash_result}")
        
        if hash_result == target_hash:
            print(f"\n🎉 PASSWORD FOUND!")
            print(f"🔑 Plaintext Password: '{pattern}'")
            return pattern
    
    # Hex patterns
    hex_patterns = [
        "0", "1", "a", "f", "00", "01", "0a", "0f", "ff",
        "000", "001", "00a", "00f", "0ff", "fff",
        "0000", "0001", "000a", "000f", "00ff", "0fff", "ffff"
    ]
    
    for pattern in hex_patterns:
        hash_result = md5_hash(pattern)
        print(f"Testing hex: '{pattern}' -> {hash_result}")
        
        if hash_result == target_hash:
            print(f"\n🎉 PASSWORD FOUND!")
            print(f"🔑 Plaintext Password: '{pattern}'")
            return pattern
    
    return None

def try_control_characters():
    """Try control characters"""
    print(f"\n🔍 TRYING CONTROL CHARACTERS")
    
    # ASCII control characters (0-31)
    for i in range(32):
        char = chr(i)
        try:
            hash_result = md5_hash(char)
            print(f"Testing control char {i}: '{repr(char)}' -> {hash_result}")
            
            if hash_result == target_hash:
                print(f"\n🎉 PASSWORD FOUND!")
                print(f"🔑 Plaintext Password: {repr(char)} (ASCII {i})")
                return char
        except:
            continue
    
    return None

def try_extended_ascii():
    """Try extended ASCII characters"""
    print(f"\n🔍 TRYING EXTENDED ASCII CHARACTERS")
    
    # Extended ASCII (128-255)
    for i in range(128, 256):
        try:
            char = chr(i)
            hash_result = md5_hash(char)
            print(f"Testing ASCII {i}: '{char}' -> {hash_result}")
            
            if hash_result == target_hash:
                print(f"\n🎉 PASSWORD FOUND!")
                print(f"🔑 Plaintext Password: '{char}' (ASCII {i})")
                return char
        except:
            continue
    
    return None

def try_byte_sequences():
    """Try raw byte sequences"""
    print(f"\n🔍 TRYING RAW BYTE SEQUENCES")
    
    # Try single bytes
    for i in range(256):
        try:
            byte_data = bytes([i])
            hash_result = hashlib.md5(byte_data).hexdigest()
            print(f"Testing byte {i}: {byte_data} -> {hash_result}")
            
            if hash_result == target_hash:
                print(f"\n🎉 PASSWORD FOUND!")
                print(f"🔑 Plaintext Password: Raw byte {i} ({byte_data})")
                return f"byte_{i}"
        except:
            continue
    
    return None

def analyze_hash_mathematically():
    """Analyze the hash mathematically"""
    print(f"\n🔍 MATHEMATICAL ANALYSIS OF HASH")
    
    hash_hex = target_hash
    print(f"Hash: {hash_hex}")
    print(f"Length: {len(hash_hex)}")
    
    # Convert to integer
    hash_int = int(hash_hex, 16)
    print(f"As integer: {hash_int}")
    print(f"As binary: {bin(hash_int)}")
    
    # Check if it's a power of 2 or has special mathematical properties
    import math
    
    if hash_int > 0:
        log2 = math.log2(hash_int)
        print(f"Log2: {log2}")
        
        if log2.is_integer():
            print(f"⚠️ Hash is a power of 2: 2^{int(log2)}")
    
    # Check for patterns
    hex_digits = list(hash_hex)
    unique_digits = set(hex_digits)
    print(f"Unique hex digits: {unique_digits}")
    print(f"Digit frequency: {[(d, hex_digits.count(d)) for d in unique_digits]}")

def main():
    print("="*80)
    print(f"🚨 CRACK SPECIFIC PATTERNS FOR MD5 HASH")
    print("🎯 Target: Try specific patterns that might produce the unusual hash with many zeros")
    print("🔥 Status: ACTIVE SPECIFIC PATTERN CRACKING")
    print("="*80)
    
    print(f"🎯 TARGET HASH: {target_hash}")
    print(f"⚠️ UNUSUAL PATTERN: {target_hash.count('0')} zeros out of 32 characters")
    
    # Mathematical analysis first
    analyze_hash_mathematically()
    
    # Try different pattern types
    password = None
    
    # Method 1: Empty and special patterns
    if not password:
        password = try_empty_and_special()
    
    # Method 2: Single characters
    if not password:
        password = try_single_characters()
    
    # Method 3: Control characters
    if not password:
        password = try_control_characters()
    
    # Method 4: Binary and hex patterns
    if not password:
        password = try_binary_patterns()
    
    # Method 5: Unicode characters
    if not password:
        password = try_unicode_characters()
    
    # Method 6: Extended ASCII
    if not password:
        password = try_extended_ascii()
    
    # Method 7: Raw byte sequences
    if not password:
        password = try_byte_sequences()
    
    # Final result
    print(f"\n🏆 SPECIFIC PATTERN CRACKING COMPLETED")
    
    if password:
        print(f"\n🎉 SUCCESS! PASSWORD CRACKED!")
        print(f"🔑 Student 4230105 Password: {repr(password)}")
        print(f"🔐 MD5 Hash: {target_hash}")
        print(f"✅ Verification: {md5_hash(password) == target_hash}")
        
        # Save result
        with open(f'FINAL_CRACKED_PASSWORD_4230105.txt', 'w') as f:
            f.write(f"FINAL PASSWORD CRACKING RESULT - STUDENT 4230105\n")
            f.write(f"=" * 50 + "\n\n")
            f.write(f"TARGET HASH: {target_hash}\n")
            f.write(f"CRACKED PASSWORD: {repr(password)}\n")
            f.write(f"PASSWORD TYPE: Special pattern\n")
            f.write(f"VERIFICATION: {md5_hash(password) == target_hash}\n\n")
            f.write(f"COMPLETE STUDENT PROFILE:\n")
            f.write(f"   ID: 4230105\n")
            f.write(f"   Name: Roaa\n")
            f.write(f"   Email: <EMAIL>\n")
            f.write(f"   Password: {repr(password)}\n")
            f.write(f"   Department: Engineering\n")
            f.write(f"   Major: Software Engineering\n")
            f.write(f"   Year: 2020 (graduated)\n")
            f.write(f"   Status: graduated\n")
        
        print(f"💾 Final result saved!")
        
    else:
        print(f"\n❌ PASSWORD STILL NOT CRACKED")
        print(f"💡 POSSIBLE EXPLANATIONS:")
        print(f"   1. The hash might be corrupted during extraction")
        print(f"   2. The password uses a very complex pattern")
        print(f"   3. The database might use a different encoding")
        print(f"   4. There might be salt added to the password")
        print(f"   5. The extraction method might have an issue")
        
        print(f"\n🔧 NEXT STEPS:")
        print(f"   - Re-verify the hash extraction")
        print(f"   - Check if database uses password salting")
        print(f"   - Try professional cracking tools")
        print(f"   - Consider that the password might be very long/complex")
    
    return password

if __name__ == "__main__":
    main()
