#!/usr/bin/env python3
"""
CRACK MD5 PASSWORD FOR STUDENT 4230105
Target: Crack the MD5 hash to find the actual plaintext password
Status: ACTIVE PASSWORD CRACKING
"""

import hashlib
import itertools
import time

# The MD5 hash we extracted
target_hash = "00003000000080030040010001000000"
target_student = "4230105"

def md5_hash(text):
    """Generate MD5 hash of text"""
    return hashlib.md5(text.encode()).hexdigest()

def try_common_passwords():
    """Try common passwords related to the student"""
    print(f"\n🔍 TRYING COMMON PASSWORDS FOR STUDENT {target_student}")
    
    # Common passwords related to student info
    common_passwords = [
        # Student ID variations
        target_student,
        f"student{target_student}",
        f"{target_student}123",
        f"{target_student}2020",
        f"spu{target_student}",
        
        # Name variations
        "roaa", "Roaa", "ROA<PERSON>",
        "roaa123", "Roaa123", "ROAA123",
        "roaa2020", "Roaa2020", "ROAA2020",
        "roaagh<PERSON>m", "RoaaGhneem", "ROAAGHNEEM",
        "roaa_ghneem", "Roaa_Ghneem", "ROAA_GHNEEM",
        "roaa.ghneem", "Roaa.Ghneem", "ROAA.GHNEEM",
        
        # University related
        "spu", "SPU", "spu123", "SPU123",
        "spu2020", "SPU2020", "spu2024", "SPU2024",
        "syria", "Syria", "SYRIA", "damascus", "Damascus",
        
        # Engineering related
        "engineering", "Engineering", "ENGINEERING",
        "software", "Software", "SOFTWARE",
        "engineer", "Engineer", "ENGINEER",
        "code", "Code", "CODE", "programming", "Programming",
        
        # Common passwords
        "password", "Password", "PASSWORD",
        "123456", "12345678", "qwerty", "abc123",
        "admin", "Admin", "ADMIN", "user", "User",
        "welcome", "Welcome", "WELCOME",
        
        # Year variations
        "2020", "2021", "2022", "2023", "2024", "2025",
        
        # Simple patterns
        "123", "1234", "12345", "111111", "000000",
        "aaa", "AAA", "abc", "ABC",
    ]
    
    print(f"🔍 Testing {len(common_passwords)} common passwords...")
    
    for password in common_passwords:
        hash_result = md5_hash(password)
        print(f"Testing: {password} -> {hash_result}")
        
        if hash_result == target_hash:
            print(f"\n🎉 PASSWORD FOUND!")
            print(f"🔑 Plaintext Password: {password}")
            print(f"🔐 MD5 Hash: {hash_result}")
            return password
    
    print(f"❌ Password not found in common passwords")
    return None

def try_numeric_passwords():
    """Try numeric passwords"""
    print(f"\n🔢 TRYING NUMERIC PASSWORDS")
    
    # Try student ID variations
    id_variations = [
        target_student,
        target_student[:6],  # 423010
        target_student[:5],  # 42301
        target_student[:4],  # 4230
        target_student[1:],  # 230105
        target_student[2:],  # 30105
        target_student[-4:], # 0105
        target_student[-3:], # 105
    ]
    
    for variation in id_variations:
        hash_result = md5_hash(variation)
        print(f"Testing ID variation: {variation} -> {hash_result}")
        
        if hash_result == target_hash:
            print(f"\n🎉 PASSWORD FOUND!")
            print(f"🔑 Plaintext Password: {variation}")
            return variation
    
    # Try simple numeric patterns
    numeric_patterns = []
    
    # 4-8 digit numbers
    for length in range(4, 9):
        # Try repeated digits
        for digit in "0123456789":
            pattern = digit * length
            numeric_patterns.append(pattern)
        
        # Try sequential numbers
        if length <= 6:
            for start in range(0, 10):
                pattern = ""
                for i in range(length):
                    pattern += str((start + i) % 10)
                numeric_patterns.append(pattern)
    
    print(f"🔍 Testing {len(numeric_patterns)} numeric patterns...")
    
    for pattern in numeric_patterns:
        hash_result = md5_hash(pattern)
        if hash_result == target_hash:
            print(f"\n🎉 PASSWORD FOUND!")
            print(f"🔑 Plaintext Password: {pattern}")
            return pattern
    
    print(f"❌ Password not found in numeric patterns")
    return None

def try_dictionary_attack():
    """Try dictionary attack with common words"""
    print(f"\n📚 TRYING DICTIONARY ATTACK")
    
    # Common Arabic/English names and words
    dictionary_words = [
        # Names
        "roaa", "ghneem", "ahmad", "mohammed", "ali", "omar", "fatima", "aisha",
        "sara", "lina", "nour", "dina", "rana", "hala", "maya", "layla",
        
        # Places
        "syria", "damascus", "aleppo", "homs", "lattakia", "spu", "university",
        
        # Common words
        "student", "password", "admin", "user", "welcome", "hello", "world",
        "computer", "science", "engineering", "software", "program", "code",
        
        # Simple words
        "love", "life", "home", "family", "friend", "happy", "good", "nice",
        "cool", "awesome", "great", "best", "top", "star", "sun", "moon"
    ]
    
    # Try single words
    for word in dictionary_words:
        for case_variant in [word.lower(), word.upper(), word.capitalize()]:
            hash_result = md5_hash(case_variant)
            if hash_result == target_hash:
                print(f"\n🎉 PASSWORD FOUND!")
                print(f"🔑 Plaintext Password: {case_variant}")
                return case_variant
    
    # Try words with numbers
    for word in dictionary_words[:20]:  # Limit to avoid too many combinations
        for num in ["1", "12", "123", "2020", "2024", "01", "00"]:
            for case_variant in [word.lower(), word.capitalize()]:
                combinations = [
                    f"{case_variant}{num}",
                    f"{num}{case_variant}",
                    f"{case_variant}_{num}",
                    f"{case_variant}.{num}"
                ]
                
                for combo in combinations:
                    hash_result = md5_hash(combo)
                    if hash_result == target_hash:
                        print(f"\n🎉 PASSWORD FOUND!")
                        print(f"🔑 Plaintext Password: {combo}")
                        return combo
    
    print(f"❌ Password not found in dictionary attack")
    return None

def try_brute_force_short():
    """Try brute force for short passwords (4-6 characters)"""
    print(f"\n💪 TRYING BRUTE FORCE FOR SHORT PASSWORDS")
    
    # Character sets
    lowercase = "abcdefghijklmnopqrstuvwxyz"
    uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    digits = "0123456789"
    
    # Try different character sets
    charsets = [
        digits,  # Numbers only
        lowercase,  # Lowercase only
        uppercase,  # Uppercase only
        digits + lowercase,  # Numbers + lowercase
        digits + uppercase,  # Numbers + uppercase
    ]
    
    for charset in charsets:
        print(f"🔍 Trying charset: {charset[:10]}{'...' if len(charset) > 10 else ''}")
        
        # Try lengths 4-6
        for length in range(4, 7):
            print(f"   Testing length {length}...")
            count = 0
            
            for password_tuple in itertools.product(charset, repeat=length):
                password = ''.join(password_tuple)
                hash_result = md5_hash(password)
                count += 1
                
                if hash_result == target_hash:
                    print(f"\n🎉 PASSWORD FOUND!")
                    print(f"🔑 Plaintext Password: {password}")
                    print(f"🔐 MD5 Hash: {hash_result}")
                    print(f"💪 Found after {count} attempts")
                    return password
                
                # Limit attempts to avoid infinite loop
                if count > 100000:  # 100k attempts max per length
                    print(f"   Reached 100k attempts, moving to next...")
                    break
    
    print(f"❌ Password not found in brute force")
    return None

def analyze_hash_pattern():
    """Analyze the hash pattern for clues"""
    print(f"\n🔍 ANALYZING HASH PATTERN")
    print(f"Target Hash: {target_hash}")
    print(f"Hash Length: {len(target_hash)}")
    print(f"Hash Type: MD5 (confirmed)")
    
    # Check for patterns in the hash
    print(f"\nHash Analysis:")
    print(f"   Starts with: {target_hash[:8]}")
    print(f"   Ends with: {target_hash[-8:]}")
    print(f"   Contains many zeros: {'Yes' if target_hash.count('0') > 10 else 'No'}")
    print(f"   Zero count: {target_hash.count('0')}/32 characters")
    
    # The hash has many zeros, which might indicate a simple password
    if target_hash.count('0') > 15:
        print(f"⚠️ Hash contains many zeros - likely a simple password!")

def main():
    print("="*80)
    print(f"🚨 CRACK MD5 PASSWORD FOR STUDENT {target_student}")
    print("🎯 Target: Crack the MD5 hash to find the actual plaintext password")
    print("🔥 Status: ACTIVE PASSWORD CRACKING")
    print("="*80)
    
    print(f"🎯 TARGET INFORMATION:")
    print(f"   Student ID: {target_student}")
    print(f"   Student Name: Roaa (contains)")
    print(f"   MD5 Hash: {target_hash}")
    print(f"   Department: Engineering")
    print(f"   Major: Software Engineering")
    print(f"   Year: 2020 (graduated)")
    
    # Analyze hash pattern
    analyze_hash_pattern()
    
    # Try different cracking methods
    password = None
    
    # Method 1: Common passwords
    if not password:
        password = try_common_passwords()
    
    # Method 2: Numeric passwords
    if not password:
        password = try_numeric_passwords()
    
    # Method 3: Dictionary attack
    if not password:
        password = try_dictionary_attack()
    
    # Method 4: Brute force (limited)
    if not password:
        password = try_brute_force_short()
    
    # Final result
    print(f"\n🏆 PASSWORD CRACKING COMPLETED")
    
    if password:
        print(f"\n🎉 SUCCESS! PASSWORD CRACKED!")
        print(f"🔑 Student {target_student} Password: {password}")
        print(f"🔐 MD5 Hash: {target_hash}")
        print(f"✅ Verification: {md5_hash(password) == target_hash}")
        
        # Save result
        with open(f'CRACKED_PASSWORD_{target_student}.txt', 'w') as f:
            f.write(f"PASSWORD CRACKING RESULT - STUDENT {target_student}\n")
            f.write(f"=" * 50 + "\n\n")
            f.write(f"CRACKING DATE: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"TARGET: Student {target_student} (Roaa Ghneem)\n")
            f.write(f"MD5 HASH: {target_hash}\n")
            f.write(f"CRACKED PASSWORD: {password}\n")
            f.write(f"VERIFICATION: {md5_hash(password) == target_hash}\n\n")
            f.write(f"STUDENT PROFILE:\n")
            f.write(f"   ID: {target_student}\n")
            f.write(f"   Name: Roaa\n")
            f.write(f"   Email: {target_student}@spu.edu.sy\n")
            f.write(f"   Password: {password}\n")
            f.write(f"   Department: Engineering\n")
            f.write(f"   Major: Software Engineering\n")
            f.write(f"   Year: 2020 (graduated)\n")
        
        print(f"💾 Result saved to: CRACKED_PASSWORD_{target_student}.txt")
        
    else:
        print(f"\n❌ PASSWORD NOT CRACKED")
        print(f"💡 The password might be:")
        print(f"   - Longer than 6 characters")
        print(f"   - Contains special characters")
        print(f"   - Uses a complex pattern")
        print(f"   - Requires more advanced cracking tools")
        print(f"\n🔧 RECOMMENDATIONS:")
        print(f"   - Use hashcat or john the ripper")
        print(f"   - Try online MD5 databases")
        print(f"   - Use rainbow tables")
        print(f"   - Perform extended brute force")
    
    return password

if __name__ == "__main__":
    main()
